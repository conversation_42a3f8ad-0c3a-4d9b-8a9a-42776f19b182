/**
 * Layout Shift Prevention Utilities
 * Helps maintain stable layouts and prevent CLS issues
 */

/**
 * Reserve space for dynamic content to prevent layout shifts
 */
export function reserveSpace(element, dimensions) {
  if (!element) return;
  
  const { width, height, minHeight } = dimensions;
  
  if (width) element.style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) element.style.height = typeof height === 'number' ? `${height}px` : height;
  if (minHeight) element.style.minHeight = typeof minHeight === 'number' ? `${minHeight}px` : minHeight;
  
  // Add containment to prevent layout shifts
  element.style.contain = 'layout style';
}

/**
 * Set stable aspect ratios for images
 */
export function setAspectRatio(element, ratio = '4/3') {
  if (!element) return;
  
  element.style.aspectRatio = ratio;
  element.style.width = '100%';
  element.style.height = 'auto';
  element.style.objectFit = 'cover';
}

/**
 * Preload critical images to prevent layout shifts
 */
export function preloadCriticalImages(imageUrls, priority = 'high') {
  if (!Array.isArray(imageUrls)) return Promise.resolve();
  
  const preloadPromises = imageUrls.map(url => {
    return new Promise((resolve) => {
      const img = new Image();
      
      // Set loading priority
      if ('fetchPriority' in img) {
        img.fetchPriority = priority;
      }
      
      // Set loading strategy
      if ('loading' in img) {
        img.loading = priority === 'high' ? 'eager' : 'lazy';
      }
      
      // Set decoding
      if ('decoding' in img) {
        img.decoding = 'async';
      }
      
      img.onload = () => resolve(url);
      img.onerror = () => resolve(url); // Resolve even on error to prevent hanging
      img.src = url;
    });
  });
  
  return Promise.all(preloadPromises);
}

/**
 * Create skeleton placeholder for content
 */
export function createSkeletonPlaceholder(container, config = {}) {
  if (!container) return;
  
  const {
    width = '100%',
    height = '200px',
    backgroundColor = '#f3f4f6',
    borderRadius = '4px',
    animation = true
  } = config;
  
  const skeleton = document.createElement('div');
  skeleton.className = 'skeleton-placeholder';
  skeleton.style.cssText = `
    width: ${typeof width === 'number' ? `${width}px` : width};
    height: ${typeof height === 'number' ? `${height}px` : height};
    background-color: ${backgroundColor};
    border-radius: ${borderRadius};
    ${animation ? 'animation: pulse 2s infinite;' : ''}
    contain: layout style paint;
  `;
  
  container.appendChild(skeleton);
  return skeleton;
}

/**
 * Remove skeleton placeholder
 */
export function removeSkeletonPlaceholder(container) {
  if (!container) return;
  
  const skeleton = container.querySelector('.skeleton-placeholder');
  if (skeleton) {
    skeleton.remove();
  }
}

/**
 * Optimize font loading to prevent layout shifts
 */
export function optimizeFontLoading() {
  // Add font-display: swap to existing font faces
  const style = document.createElement('style');
  style.textContent = `
    @font-face {
      font-family: 'Inter';
      font-display: swap;
    }
    @font-face {
      font-family: 'Montserrat';
      font-display: swap;
    }
    @font-face {
      font-family: 'Bebas Neue';
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Prevent layout shifts from dynamic content
 */
export function stabilizeContainer(element, options = {}) {
  if (!element) return;
  
  const {
    minHeight = '100px',
    contain = 'layout style',
    willChange = 'auto'
  } = options;
  
  element.style.minHeight = typeof minHeight === 'number' ? `${minHeight}px` : minHeight;
  element.style.contain = contain;
  element.style.willChange = willChange;
}

/**
 * Measure and reserve space for content before it loads
 */
export function measureAndReserve(element, contentLoader) {
  if (!element || typeof contentLoader !== 'function') return;
  
  // Create a hidden clone to measure content
  const clone = element.cloneNode(true);
  clone.style.cssText = `
    position: absolute;
    top: -9999px;
    left: -9999px;
    visibility: hidden;
    pointer-events: none;
  `;
  
  document.body.appendChild(clone);
  
  // Load content in clone
  contentLoader(clone).then(() => {
    const rect = clone.getBoundingClientRect();
    
    // Reserve space in original element
    reserveSpace(element, {
      width: rect.width,
      height: rect.height
    });
    
    // Remove clone
    document.body.removeChild(clone);
    
    // Now load content in original element
    contentLoader(element);
  });
}

/**
 * Create stable grid layout
 */
export function createStableGrid(container, itemCount, itemAspectRatio = '1/1') {
  if (!container) return;
  
  // Clear existing content
  container.innerHTML = '';
  
  // Create grid items with stable dimensions
  for (let i = 0; i < itemCount; i++) {
    const item = document.createElement('div');
    item.className = 'grid-item';
    item.style.cssText = `
      aspect-ratio: ${itemAspectRatio};
      background-color: #f3f4f6;
      border-radius: 8px;
      contain: layout style paint;
      animation: pulse 2s infinite;
    `;
    container.appendChild(item);
  }
}

/**
 * Monitor and log layout shifts
 */
export function monitorLayoutShifts() {
  if (!('PerformanceObserver' in window)) return;
  
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (!entry.hadRecentInput) {
        console.warn('Layout shift detected:', {
          value: entry.value,
          sources: entry.sources?.map(source => ({
            element: source.node,
            previousRect: source.previousRect,
            currentRect: source.currentRect
          }))
        });
      }
    }
  });
  
  observer.observe({ entryTypes: ['layout-shift'] });
  
  return observer;
}

/**
 * Initialize layout shift prevention
 */
export function initLayoutShiftPrevention() {
  // Add CSS for layout shift prevention
  const style = document.createElement('style');
  style.textContent = `
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    
    .prevent-layout-shift {
      contain: layout style paint;
    }
    
    .stable-aspect-ratio {
      aspect-ratio: var(--aspect-ratio, 1/1);
      width: 100%;
      height: auto;
    }
    
    .skeleton-placeholder {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }
  `;
  document.head.appendChild(style);
  
  // Optimize font loading
  optimizeFontLoading();
  
  // Start monitoring layout shifts in development
  if (process.env.NODE_ENV === 'development') {
    monitorLayoutShifts();
  }
}

export default {
  reserveSpace,
  setAspectRatio,
  preloadCriticalImages,
  createSkeletonPlaceholder,
  removeSkeletonPlaceholder,
  optimizeFontLoading,
  stabilizeContainer,
  measureAndReserve,
  createStableGrid,
  monitorLayoutShifts,
  initLayoutShiftPrevention
};
