import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  ShoppingBag,
  ChevronLeft,
  Minus,
  Plus,
  Trash2,
  CreditCard,
  ShieldCheck,
  Truck,
  CheckCircle,
  Mail
} from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import { toast } from 'react-toastify';
// No need for AddressForm as we use the profile's address

// Global image cache for instant loading across cart page
const cartImageCache = new Map();
const cartPreloadedImages = new Set();

// Enhanced image optimization utility for cart page
const optimizeCartImageUrl = (url, width = 800, height = 800, quality = 'good') => {
  if (!url) return 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';

  const cacheKey = `${url}-${width}-${height}-${quality}`;

  if (cartImageCache.has(cacheKey)) {
    return cartImageCache.get(cacheKey);
  }

  let optimizedUrl;

  if (url.includes('cloudinary.com')) {
    optimizedUrl = url.replace('/upload/', `/upload/w_${width},h_${height},c_limit,f_webp,q_auto:${quality},fl_progressive,dpr_auto,fl_immutable_cache,fl_awebp/`);
  } else if (url.includes('unsplash.com')) {
    optimizedUrl = `${url}&w=${width}&h=${height}&fit=crop&auto=format&q=85`;
  } else {
    optimizedUrl = url;
  }

  cartImageCache.set(cacheKey, optimizedUrl);
  return optimizedUrl;
};

// SUPER AGGRESSIVE image preloading function for cart page
const preloadCartImages = (items) => {
  if (!items || items.length === 0) return;

  const preloadPromises = [];

  items.forEach((item, index) => {
    // Preload ALL cart item images for instant experience
    if (item.image) {
      const optimizedUrl = optimizeCartImageUrl(item.image, 800, 1000, 'good');
      if (!cartPreloadedImages.has(optimizedUrl)) {
        cartPreloadedImages.add(optimizedUrl);
        preloadPromises.push(new Promise((resolve) => {
          const img = new Image();
          img.onload = () => resolve();
          img.onerror = () => resolve();
          img.src = optimizedUrl;
          // Enable faster loading with fetch priority
          if ('fetchPriority' in img) {
            img.fetchPriority = 'high';
          }
          if ('decoding' in img) {
            img.decoding = 'async';
          }
          if ('loading' in img) {
            img.loading = 'eager';
          }
        }));
      }
    }

    // Preload additional images if available
    if (item.images && item.images.length > 0) {
      item.images.forEach(imageUrl => {
        const optimizedUrl = optimizeCartImageUrl(imageUrl, 800, 1000, 'good');
        if (!cartPreloadedImages.has(optimizedUrl)) {
          cartPreloadedImages.add(optimizedUrl);
          preloadPromises.push(new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve();
            img.onerror = () => resolve();
            img.src = optimizedUrl;
          }));
        }
      });
    }
  });

  // Wait for critical images to load
  return Promise.all(preloadPromises);
};

function CartPage() {
  const navigate = useNavigate();
  const { items, removeFromCart, updateQuantity, clearCart, subtotal } = useCart();
  const { user, isAuthenticated, refreshProfile } = useAuth();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [animateTotal, setAnimateTotal] = useState(false);
  const [orderConfirmed, setOrderConfirmed] = useState(false);
  const [orderDetails, setOrderDetails] = useState(null);
  const [imagesPreloaded, setImagesPreloaded] = useState(false);
  const [razorpayLoaded, setRazorpayLoaded] = useState(false);
  // No need for address form state variables as we use the profile's address

  // Load Razorpay script
  useEffect(() => {
    const loadRazorpayScript = () => {
      return new Promise((resolve) => {
        if (window.Razorpay) {
          setRazorpayLoaded(true);
          resolve(true);
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.onload = () => {
          setRazorpayLoaded(true);
          resolve(true);
        };
        script.onerror = () => {
          console.error('Failed to load Razorpay script');
          toast.error('Failed to load payment gateway');
          resolve(false);
        };
        document.body.appendChild(script);
      });
    };

    loadRazorpayScript();
  }, []);

  // SUPER AGGRESSIVE preloading for cart items
  useEffect(() => {
    if (items.length === 0) return;

    const preloadAllImages = async () => {
      try {
        // Preload all cart item images
        await preloadCartImages(items);
        setImagesPreloaded(true);
      } catch (error) {
        console.error('Failed to preload cart images:', error);
        setImagesPreloaded(true); // Continue even if preloading fails
      }
    };

    preloadAllImages();
  }, [items]);

  // Helper to extract a usable shipping address from the user object
  const extractShippingAddress = (user) => {
    if (!user) return null;
    // 1. Direct shipping_address field
    if (user.shipping_address && Object.keys(user.shipping_address).length > 0) {
      return {
        line1: user.shipping_address.line1 || user.shipping_address.street || user.shipping_address.address_line_1 || '',
        line2: user.shipping_address.line2 || user.shipping_address.address_line_2 || '',
        city: user.shipping_address.city || '',
        state: user.shipping_address.state || '',
        postal_code: user.shipping_address.postal_code || user.shipping_address.zip || '',
        country: user.shipping_address.country || 'India'
      };
    }
    // 2. Fallback to addresses array
    if (Array.isArray(user.addresses)) {
      const found = user.addresses.find(
        (addr) => addr.type === 'shipping' || addr.type === 'both' || addr.is_default
      );
      if (found && Object.keys(found).length > 0) {
        return {
          line1: found.address_line_1 || found.line1 || found.street || '',
          line2: found.address_line_2 || found.line2 || '',
          city: found.city || '',
          state: found.state || '',
          postal_code: found.postal_code || found.zip || '',
          country: found.country || 'India'
        };
      }
    }
    return null;
  };

  // Email validation function
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  // Handle address form submission
  // No need for address form handling as we use the profile's address

  useEffect(() => {
    setAnimateTotal(true);
    const timer = setTimeout(() => setAnimateTotal(false), 500);
    return () => clearTimeout(timer);
  }, [subtotal]);

  const handleCheckout = async () => {
    console.log('🛒 handleCheckout - Starting checkout process');
    console.log('🛒 Auth state:', { isAuthenticated, user: user?.email, userId: user?.id });

    // 1. User Authentication Check
    if (!isAuthenticated || !user) {
      console.error('🛒 Authentication required - User not logged in');
      toast.error('You must be logged in to place an order.');
      navigate('/login');
      return;
    }

    // 1.5. Refresh user profile to get latest data
    console.log('🛒 Refreshing user profile...');
    try {
      const profileRefresh = await refreshProfile();
      if (profileRefresh.success) {
        console.log('🛒 Profile refreshed successfully');
        // Small delay to ensure state is updated
        await new Promise(resolve => setTimeout(resolve, 100));
      } else {
        console.warn('🛒 Profile refresh failed, continuing with cached data');
      }
    } catch (error) {
      console.warn('🛒 Profile refresh error, continuing with cached data:', error);
    }

    // 2. Profile Completeness Check (after refresh)
    console.log('🛒 Current user data after refresh:', {
      email: user?.email,
      phone: user?.phone,
      first_name: user?.first_name,
      last_name: user?.last_name,
      name: user?.name
    });

    const missingFields = [];
    if (!user.email || user.email.trim() === '') {
      missingFields.push('Email');
    }
    if (!user.phone || user.phone.trim() === '') {
      missingFields.push('Mobile Number');
    }
    if (!user.first_name || user.first_name.trim() === '') {
      missingFields.push('First Name');
    }
    if (!user.last_name || user.last_name.trim() === '') {
      missingFields.push('Last Name');
    }

    if (missingFields.length > 0) {
      console.error('🛒 Profile incomplete - Missing fields:', missingFields);
      toast.error(`Please complete your profile. Missing: ${missingFields.join(', ')}`);
      navigate('/profile', {
        state: {
          message: `Please complete your profile to proceed with checkout. Missing: ${missingFields.join(', ')}`,
          missingFields: missingFields,
          returnTo: '/cart'
        }
      });
      return;
    }

    // 2. Cart Validation
    console.log('🛒 Cart items:', items);
    console.log('🛒 Cart subtotal:', subtotal);

    if (items.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    // 3. Profile Completeness Validation
    // 3.1 Email validation
    if (!user.email || user.email.trim() === '' || !isValidEmail(user.email)) {
      console.error('🛒 Profile incomplete - Missing valid email');
      alert('Please complete your profile with a valid email address before placing an order.');
      navigate('/profile');
      return;
    }

    // 3.2 Phone validation
    if (!user.phone || user.phone.trim() === '') {
      console.error('🛒 Profile incomplete - Missing phone number');
      alert('Please complete your profile with a valid phone number before placing an order.');
      navigate('/profile');
      return;
    }

    // 3.3 Name validation
    const customerName = user.name || `${user.first_name || ''} ${user.last_name || ''}`.trim();
    if (!customerName) {
      console.error('🛒 Profile incomplete - Missing name');
      alert('Please complete your profile with your name before placing an order.');
      navigate('/profile');
      return;
    }

    // 3.4 Shipping address validation
    const shippingAddress = extractShippingAddress(user);
    console.log('🛒 Extracted shipping address:', shippingAddress);

    const requiredAddressFields = ['line1', 'city', 'state', 'postal_code', 'country'];
    const missingAddressFields = requiredAddressFields.filter(
      (field) => !shippingAddress || !shippingAddress[field] || shippingAddress[field].toString().trim() === ''
    );

    if (missingAddressFields.length > 0) {
      console.error('🛒 Profile incomplete - Missing shipping address fields:', missingAddressFields);
      console.error('🛒 Current address data:', shippingAddress);
      toast.error(`Please complete your shipping address. Missing: ${missingAddressFields.join(', ')}`);
      navigate('/profile', {
        state: {
          message: `Please complete your shipping address to proceed with checkout. Missing: ${missingAddressFields.join(', ')}`,
          missingFields: missingAddressFields,
          returnTo: '/cart',
          focusSection: 'address'
        }
      });
      return;
    }

    // Use the extracted shipping address for order creation
    const shippingAddressFromProfile = shippingAddress;

    // 4. Start checkout process
    setIsCheckingOut(true);

    try {
      // Check if Razorpay is loaded
      if (!window.Razorpay) {
        toast.error('Payment gateway not loaded. Please refresh and try again.');
        return;
      }

      // Calculate totals (prices are inclusive of taxes)
      const total = subtotal;

      // 5. Get the authentication token
      const getAuthToken = () => {
        try {
          const storedTokens = localStorage.getItem('wolffoxx_tokens');
          if (storedTokens) {
            const tokens = JSON.parse(storedTokens);
            return tokens.access_token || tokens.accessToken;
          }
          return null;
        } catch (error) {
          console.error('Error getting auth token:', error);
          return null;
        }
      };

      const authToken = getAuthToken();
      console.log('🛒 Auth token check:', {
        tokenExists: !!authToken,
        tokenPreview: authToken ? authToken.substring(0, 20) + '...' : 'none',
        storedTokens: localStorage.getItem('wolffoxx_tokens') ? 'exists' : 'missing'
      });

      if (!authToken) {
        toast.error('Authentication token not found. Please log in again.');
        navigate('/login');
        return;
      }

      // 6. Create Razorpay order first
      console.log('🛒 Creating Razorpay order...');
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';
      const orderResponse = await axios.post(`${API_BASE_URL}/payments/create-order`, {
        amount: Math.round(total * 100), // Convert to paise
        currency: 'INR'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });

      console.log('🛒 Razorpay order response:', orderResponse.data);

      // Extract the actual data from the nested response structure
      const responseData = orderResponse.data.data || orderResponse.data;
      console.log('🛒 Extracted response data:', responseData);

      if (!responseData || !responseData.razorpay_order_id) {
        console.error('🛒 Invalid response structure:', responseData);
        console.error('🛒 Available keys:', Object.keys(responseData || {}));
        throw new Error('Invalid response from payment gateway');
      }

      const { razorpay_order_id, amount, currency, key_id } = responseData;

      // Validate required fields
      if (!razorpay_order_id || !amount || !currency || !key_id) {
        throw new Error('Missing required payment data from server');
      }

      // 6. Prepare order data for verification after payment
      const orderData = {
        // Customer details
        customer_name: customerName,
        customer_email: user.email,
        customer_phone: user.phone,

        // Addresses - Use the shipping address from the user profile
        shipping_address: shippingAddressFromProfile,
        billing_address: shippingAddressFromProfile, // Always use shipping address for billing

        // Payment and pricing details
        payment_method: 'razorpay',
        subtotal: subtotal,
        total_amount: total,
        tax_amount: 0, // Prices are inclusive of taxes
        shipping_amount: 0,
        discount_amount: 0,

        // Additional info
        order_notes: 'Order from cart page - Razorpay payment',

        // Cart items for order_items table
        items: items.map(item => ({
          product_id: item.id,
          selected_color: item.color,
          selected_size: item.size,
          selected_color_hex: item.colorHex,
          quantity: item.quantity,
          unit_price: parseFloat(item.price) || 0,
          sale_price: parseFloat(item.salePrice) || 0,
          total_price: (parseFloat(item.salePrice || item.price) || 0) * item.quantity,
          product_name: item.name,
          product_image: item.image,
          product_sku: item.sku || `SKU-${item.id}`,
          product_category: item.category || 'Uncategorized',
          outfit_id: item.outfitId,
          outfit_name: item.outfitName
        }))
      };

      // 7. Configure Razorpay options
      const options = {
        key: key_id,
        amount: amount.toString(),
        currency: currency,
        name: 'Wolffoxx',
        description: 'Fashion Order Payment',
        order_id: razorpay_order_id,
        handler: async function (response) {
          try {
            console.log('💳 Payment successful, verifying...');

            // Verify payment and create order
            const verifyResponse = await axios.post(`${API_BASE_URL}/payments/verify`, {
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_order_id: response.razorpay_order_id,
              razorpay_signature: response.razorpay_signature,
              order_data: orderData
            }, {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
              }
            });

            console.log('✅ Payment verified and order created:', verifyResponse.data);

            // Set order details for confirmation display
            const orderDetails = {
              order_number: verifyResponse.data.order_number,
              payment_id: response.razorpay_payment_id,
              total_amount: total,
              subtotal: subtotal,
              tax_amount: 0, // Prices are inclusive of taxes
              shipping_amount: 0,
              items: items.map(item => ({
                name: item.name,
                quantity: item.quantity,
                price: parseFloat(item.salePrice || item.price) || 0,
                color: item.color,
                size: item.size,
                total: (parseFloat(item.salePrice || item.price) || 0) * item.quantity
              })),
              customer_email: user.email,
              customer_name: customerName,
              shipping_address: shippingAddressFromProfile
            };

            setOrderDetails(orderDetails);

            // Clear cart and show confirmation
            clearCart();
            setOrderConfirmed(true);
            toast.success('Payment successful! Order placed successfully.');

          } catch (error) {
            console.error('Payment verification error:', error);
            toast.error(error.response?.data?.message || 'Payment verification failed');
          } finally {
            setIsCheckingOut(false);
          }
        },
        prefill: {
          name: customerName,
          email: user.email,
          contact: user.phone
        },
        theme: {
          color: '#FF6F35'
        },
        modal: {
          ondismiss: function() {
            console.log('Payment modal dismissed');
            setIsCheckingOut(false);
          }
        },
        notes: {
          source: 'cart_page'
        }
      };

      // 8. Open Razorpay payment modal
      const rzp = new window.Razorpay(options);

      rzp.on('payment.failed', function(response) {
        console.error('Payment failed:', response.error);
        toast.error(`Payment failed: ${response.error.description || 'Unknown error'}`);
        setIsCheckingOut(false);
      });

      rzp.open();

    } catch (error) {
      console.error('🚨 Payment initialization failed:', error);
      console.error('🚨 Error response:', error.response?.data);
      console.error('🚨 Error status:', error.response?.status);

      // Handle specific error cases
      if (error.response?.status === 401) {
        toast.error('You must be logged in to place an order.');
        navigate('/login');
      } else if (error.response?.status === 400) {
        toast.error(error.response?.data?.message || 'Invalid request. Please try again.');
      } else if (error.response?.data?.message?.includes('Cart is empty')) {
        toast.error('Your cart is empty. Please add items before checkout.');
      } else if (error.response?.data?.message?.includes('Amount mismatch')) {
        toast.error('Cart total mismatch. Please refresh and try again.');
        window.location.reload();
      } else if (error.message?.includes('Missing required payment data')) {
        toast.error('Payment gateway error. Please try again.');
      } else {
        toast.error(error.response?.data?.message || error.message || 'Failed to initialize payment. Please try again.');
      }
      setIsCheckingOut(false);
    }
  };

  const handleRemoveItem = (itemId, color, size) => {
    removeFromCart(itemId, color, size);
    setShowDeleteConfirm(null);
  };

  const handleQuantityDecrease = (item) => {
    if (item.quantity > 1) {
      updateQuantity(item.id, item.quantity - 1, item.color, item.size);
    }
  };

  const handleQuantityIncrease = (item) => {
    updateQuantity(item.id, item.quantity + 1, item.color, item.size);
  };



  // Prices are inclusive of taxes
  const total = subtotal;

  // Order Confirmation Component
  if (orderConfirmed && orderDetails) {
    // Calculate estimated delivery date (7 days from now)
    const estimatedDelivery = new Date();
    estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);
    
    return (
      <div className="min-h-screen bg-[#000000]">
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <div className="bg-green-500 p-6 rounded-full">
                <CheckCircle size={48} className="text-white" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Order Confirmed!
            </h1>
            <p className="text-[#AAAAAA] text-lg mb-2">
              Thank you for your order, {orderDetails.customer_name}
            </p>
            <p className="text-[#AAAAAA] mb-6">
              Order #{orderDetails.order_number}
            </p>
          </div>

          <div className="bg-[#1a1a1a] rounded-xl p-6 border border-[#404040] mb-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Mail size={20} />
              Confirmation Email Sent
            </h2>
            <p className="text-[#AAAAAA] mb-4">
              A confirmation email has been sent to <span className="text-white font-medium">{orderDetails.customer_email}</span> with your order details and tracking information.
            </p>
            
            {/* Order Summary */}
            <div className="bg-[#2a2a2a] p-4 rounded-lg mb-4">
              <h3 className="text-white font-medium mb-3">Order Summary</h3>
              <div className="space-y-2">
                {orderDetails.items.map((item, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">
                      {item.name} ({item.color}, {item.size}) x {item.quantity}
                    </span>
                    <span className="text-white">₹{item.total.toFixed(2)}</span>
                  </div>
                ))}
                <div className="border-t border-[#404040] pt-2 mt-2 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">Subtotal</span>
                    <span className="text-white">₹{orderDetails.subtotal.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">Shipping</span>
                    <span className="text-white">{orderDetails.shipping_amount > 0 ? `₹${orderDetails.shipping_amount.toFixed(2)}` : 'Free'}</span>
                  </div>
                  <div className="border-t border-[#404040] pt-2">
                    <div className="flex justify-between font-semibold">
                      <span className="text-white">Total</span>
                      <span className="text-white">₹{orderDetails.total_amount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Shipping Information */}
            {orderDetails.shipping_address && (
              <div className="bg-[#2a2a2a] p-4 rounded-lg mb-4">
                <h3 className="text-white font-medium mb-3 flex items-center gap-2">
                  <Truck size={16} className="text-amber-500" />
                  Shipping Information
                </h3>
                <div className="text-sm text-[#AAAAAA]">
                  <p className="text-white font-medium">{orderDetails.customer_name}</p>
                  <p>{orderDetails.shipping_address.line1 || orderDetails.shipping_address.street}</p>
                  {orderDetails.shipping_address.line2 && <p>{orderDetails.shipping_address.line2}</p>}
                  <p>
                    {orderDetails.shipping_address.city}, {orderDetails.shipping_address.state} {orderDetails.shipping_address.postal_code || orderDetails.shipping_address.zip}
                  </p>
                  <p>{orderDetails.shipping_address.country}</p>
                </div>
              </div>
            )}
            
            {/* Estimated Delivery */}
            <div className="bg-[#2a2a2a] p-4 rounded-lg">
              <h3 className="text-white font-medium mb-3 flex items-center gap-2">
                <Truck size={16} className="text-emerald-500" />
                Estimated Delivery
              </h3>
              <p className="text-[#AAAAAA] text-sm">
                Your order should arrive by <span className="text-white font-medium">
                  {estimatedDelivery.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </span>
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => navigate('/orders')}
              className="px-6 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white font-medium rounded-lg hover:from-[#1a3f9e] hover:to-[#4a9dd4] transition-all duration-300"
            >
              View Order History
            </button>
            <button
              onClick={() => navigate('/collections')}
              className="px-6 py-3 bg-[#2a2a2a] text-white font-medium rounded-lg hover:bg-[#404040] transition-colors border border-[#404040]"
            >
              Continue Shopping
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#000000]">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Mobile Header */}
        <div className="mb-6 flex items-center justify-between">
          <button
            onClick={() => navigate(-1)}
            className="text-[#AAAAAA] hover:text-white flex items-center gap-2 transition-all duration-300 group"
          >
            <div className="bg-[#2a2a2a] p-2 rounded-full group-hover:bg-[#404040] transition-colors">
              <ChevronLeft size={16} />
            </div>
            <span className="font-medium hidden sm:inline">Continue Shopping</span>
          </button>
          {/* Mobile trust badges */}
          <div className="flex items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-1 text-[#AAAAAA] text-xs sm:text-sm">
              <ShieldCheck size={16} className="text-emerald-500" />
              <span className="hidden sm:inline">Secure</span>
            </div>
            <div className="flex items-center gap-1 text-[#AAAAAA] text-xs sm:text-sm">
              <Truck size={16} className="text-amber-500" />
              <span className="hidden sm:inline">Free Ship</span>
            </div>
          </div>
        </div>

        {/* Page Title */}
        <div className="mb-8 border-b border-[#2a2a2a] pb-6">
          <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2 tracking-tighter">
            Your <span className="text-[#AAAAAA]">Cart</span>
          </h1>
          <p className="text-[#AAAAAA] text-sm sm:text-base">
            {items.length} {items.length === 1 ? 'item' : 'items'} in your shopping bag
          </p>
        </div>

        {items.length === 0 ? (
          <div className="text-center py-16 bg-[#1a1a1a] rounded-xl border border-[#404040] backdrop-blur-sm shadow-xl">
            <div className="flex justify-center mb-6">
              <div className="bg-[#2a2a2a] p-6 rounded-full">
                <ShoppingBag size={40} className="text-[#AAAAAA]" />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl text-white mb-4 font-bold">Your cart is empty</h2>
            <p className="text-[#AAAAAA] mb-8 max-w-md mx-auto text-sm sm:text-base px-4">
              Looks like you haven't added anything to your cart yet. Discover our premium selection and find something amazing.
            </p>
            <button
              onClick={() => navigate('/collections')}
              className="px-8 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E]  text-white font-medium rounded-lg transition-all duration-300 shadow-lg"
            >
              Start Shopping
            </button>
          </div>
        ) : (
          <div className="space-y-6 lg:grid lg:grid-cols-12 lg:gap-8 lg:space-y-0">
            {/* Cart Items - Mobile First */}
            <div className="lg:col-span-8">
              <div className="bg-[#1a1a1a] backdrop-blur-md rounded-xl overflow-hidden shadow-xl border border-[#404040]">
                {/* Desktop Header - Hidden on Mobile */}
                <div className="hidden md:block p-6 border-b border-[#2a2a2a]">
                  <div className="flex justify-between text-[#AAAAAA] text-sm font-medium">
                    <span className="uppercase tracking-wider">Product</span>
                    <div className="grid grid-cols-4 gap-3 w-[320px]">
                      <span className="text-center uppercase tracking-wider">Price</span>
                      <span className="text-center uppercase tracking-wider">Qty</span>
                      <span className="text-center uppercase tracking-wider">Total</span>
                      {/* <span className="text-center uppercase tracking-wider">Remove</span> */}
                    </div>
                  </div>
                </div>

                {/* Cart Items */}
                {items.map((item) => (
                  <div
                    key={`${item.id}-${item.color}-${item.size}`}
                    className="p-3 sm:p-6 border-b border-[#2a2a2a] hover:bg-[#2a2a2a] transition-colors duration-300"
                  >
                    {/* Mobile Layout */}
                    <div className="md:hidden">
                      <div className="flex gap-3 mb-4">
                        <Link
                          to={`/product/${item.id}`}
                          className="relative overflow-hidden rounded-lg w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-gray-800 to-gray-900 shadow-lg flex-shrink-0 hover:scale-105 transition-transform duration-300"
                        >
                          <img
                            src={optimizeCartImageUrl(item.image, 800, 1000, 'good')}
                            alt={item.name}
                            className="w-full h-full object-cover"
                            loading="eager"
                            fetchPriority="high"
                            onError={(e) => {
                              console.log('Cart image failed to load:', e.target.src);
                              e.target.src = 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';
                            }}
                          />
                        </Link>
                        <div className="flex-1 min-w-0 pr-3">
                          <Link to={`/product/${item.id}`}>
                            <h3 className="text-white font-medium text-sm sm:text-base mb-2 hover:text-cyan-400 transition-colors leading-tight">
                              {item.name.length > 40 ? `${item.name.substring(0, 40)}...` : item.name}
                            </h3>
                          </Link>
                          <div className="text-[#AAAAAA] text-sm space-y-1">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full border border-[#404040]"
                                style={{
                                  backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                                  opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                                }}
                              ></div>
                              <span>{item.color}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 rounded-sm border border-[#404040] flex items-center justify-center text-[9px]">
                                {item.size}
                              </div>
                              <span>Size: {item.size}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0 min-w-[100px] flex flex-col items-end">
                          <div className="text-white font-medium text-base sm:text-lg mb-3">
                            ₹{(parseFloat(item.salePrice || item.price) || 0).toFixed(2)}
                          </div>
                          {showDeleteConfirm === `${item.id}-${item.color}-${item.size}` ? (
                            <div className="flex items-center gap-1">
                              <button
                                className="bg-red-600 text-white p-2 rounded-md hover:bg-red-700 transition-colors active:scale-95"
                                onClick={() => handleRemoveItem(item.id, item.color, item.size)}
                              >
                                <Trash2 size={14} />
                              </button>
                              <button
                                className="bg-[#404040] text-white p-2 rounded-md hover:bg-[#6a6a6a] transition-colors active:scale-95"
                                onClick={() => setShowDeleteConfirm(null)}
                              >
                                <ChevronLeft size={14} />
                              </button>
                            </div>
                          ) : (
                            <button
                              className="text-red-400 hover:text-red-500 transition-colors p-2 hover:scale-110 active:scale-95 bg-[#2a2a2a] rounded-md"
                              onClick={() => setShowDeleteConfirm(`${item.id}-${item.color}-${item.size}`)}
                            >
                              <Trash2 size={16} />
                            </button>
                          )}
                        </div>
                      </div>
                      {/* Mobile Quantity Controls */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <button
                            className="w-10 h-10 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-l-md transition-colors active:scale-95"
                            onClick={() => handleQuantityDecrease(item)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={16} />
                          </button>
                          <div className="w-12 h-10 bg-[#2a2a2a] flex items-center justify-center text-white font-medium">
                            {item.quantity}
                          </div>
                          <button
                            className="w-10 h-10 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-r-md transition-colors active:scale-95"
                            onClick={() => handleQuantityIncrease(item)}
                          >
                            <Plus size={16} />
                          </button>
                        </div>
                        <div className="text-white font-semibold text-lg">
                          ₹{((parseFloat(item.salePrice || item.price) || 0) * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    </div>

                    {/* Desktop Layout */}
                    <div className="hidden md:flex md:items-center justify-between">
                      <div className="flex items-center gap-6">
                        <Link
                          to={`/product/${item.id}`}
                          className="relative overflow-hidden rounded-lg w-24 h-24 bg-gradient-to-br from-gray-800 to-gray-900 shadow-lg hover:scale-105 transition-transform duration-300"
                        >
                          <img
                            src={optimizeCartImageUrl(item.image, 800, 1000, 'good')}
                            alt={item.name}
                            className="w-full h-full object-cover"
                            loading="eager"
                            fetchPriority="high"
                            onError={(e) => {
                              console.log('Cart image failed to load:', e.target.src);
                              e.target.src = 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';
                            }}
                          />
                        </Link>
                        <div className="min-w-0 flex-1">
                          <Link to={`/product/${item.id}`}>
                            <h3 className="text-white hover:text-cyan-400 font-medium text-lg transition-colors duration-300 cursor-pointer leading-tight">
                              {item.name.length > 30 ? `${item.name.substring(0, 30)}...` : item.name}
                            </h3>
                          </Link>
                          <div className="text-[#AAAAAA] text-sm mt-2 space-y-1">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full border border-[#404040]"
                                style={{
                                  backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                                  opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                                }}
                              ></div>
                              <span>{item.color}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 rounded-sm border border-[#404040] flex items-center justify-center text-[9px]">
                                {item.size}
                              </div>
                              <span>Size: {item.size}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-4 gap-3 items-center w-[320px]">
                        <div className="text-white text-center font-medium">
                          ₹{(parseFloat(item.salePrice || item.price) || 0).toFixed(2)}
                        </div>
                        <div className="flex items-center justify-center">
                          <button
                            className="w-7 h-7 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-l-md transition-colors active:scale-95"
                            onClick={() => handleQuantityDecrease(item)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={12} />
                          </button>
                          <div className="w-8 h-7 bg-[#2a2a2a] flex items-center justify-center text-white font-medium text-sm">
                            {item.quantity}
                          </div>
                          <button
                            className="w-7 h-7 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-r-md transition-colors active:scale-95"
                            onClick={() => handleQuantityIncrease(item)}
                          >
                            <Plus size={12} />
                          </button>
                        </div>
                        <div className="text-white font-medium text-center">
                          ₹{((parseFloat(item.salePrice || item.price) || 0) * item.quantity).toFixed(2)}
                        </div>
                        <div className="flex justify-center">
                          {showDeleteConfirm === `${item.id}-${item.color}-${item.size}` ? (
                            <div className="flex items-center gap-1">
                              <button
                                className="bg-red-600 text-white p-1.5 rounded-md hover:bg-red-700 transition-colors"
                                onClick={() => handleRemoveItem(item.id, item.color, item.size)}
                              >
                                <Trash2 size={12} />
                              </button>
                              <button
                                className="bg-[#404040] text-white p-1.5 rounded-md hover:bg-[#6a6a6a] transition-colors"
                                onClick={() => setShowDeleteConfirm(null)}
                              >
                                <ChevronLeft size={12} />
                              </button>
                            </div>
                          ) : (
                            <button
                              className="text-[#AAAAAA] hover:text-red-500 transition-colors p-1.5 hover:scale-110 active:scale-95 bg-[#2a2a2a] rounded-md"
                              onClick={() => setShowDeleteConfirm(`${item.id}-${item.color}-${item.size}`)}
                            >
                              <Trash2 size={14} />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="p-4 sm:p-6 flex justify-end">
                  <button
                    onClick={clearCart}
                    className="text-[#AAAAAA] hover:text-red-500 text-sm flex items-center gap-1 transition-colors duration-300 p-2"
                  >
                    <Trash2 size={14} />
                    <span>Clear cart</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-4">
              <div className="bg-[#1a1a1a] backdrop-blur-md rounded-xl p-4 sm:p-6 border border-[#404040] shadow-xl lg:sticky lg:top-8">
                <h2 className="text-xl sm:text-2xl font-semibold text-white mb-6 sm:mb-8 border-b border-[#2a2a2a] pb-4">
                  Order Summary
                </h2>
                <div className="space-y-3 sm:space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Subtotal</span>
                    <span className="text-white font-medium">₹{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Shipping</span>
                    <span className="text-white font-medium">Free</span>
                  </div>

                </div>
                <div className="border-t border-[#2a2a2a] pt-4 sm:pt-6 mb-6">
                  <div className="flex justify-between text-lg sm:text-xl font-semibold">
                    <span className="text-white">Total</span>
                    <span
                      className={`text-white transition-all duration-500 ${
                        animateTotal ? 'scale-110 text-gray-300' : ''
                      }`}
                    >
                      ₹{total.toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* Shipping Address Section */}
                <div className="mb-6 border-t border-[#2a2a2a] pt-4">
                  {/* <div className="flex justify-between items-center mb-3">
                    <h3 className="text-white font-medium">Shipping Address</h3>
                    <button 
                      onClick={() => navigate('/profile')}
                      className="text-cyan-400 hover:text-cyan-300 flex items-center gap-1 text-sm"
                    >
                      <Edit size={14} />
                      {user?.shipping_address && user.shipping_address.city ? 'Edit' : 'Add'}
                    </button>
                  </div> */}
                                    
                  {/* Note about billing address */}
                  <div className="mt-3 flex items-center">
                    <input
                      type="checkbox"
                      id="sameAsBilling"
                      checked={true}
                      disabled={true}
                      className="mr-2 h-4 w-4 accent-cyan-500"
                    />
                    <label htmlFor="sameAsBilling" className="text-[#AAAAAA] text-sm">
                      Using shipping address for billing
                    </label>
                  </div>
                </div>
                <button
                  className={`w-full py-4 bg-[#FF6F35]  text-white font-medium rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-lg mb-6 text-base sm:text-lg ${
                    isCheckingOut ? 'opacity-75 cursor-wait' : 'hover:scale-105 active:scale-95'
                  }`}
                  onClick={handleCheckout}
                  disabled={isCheckingOut}
                >
                  {isCheckingOut ? (
                    <>
                      <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin " />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard size={18}/>
                      Complete Order
                    </>
                  )}
                </button>



                {/* Payment Methods */}
<div className="grid grid-cols-3 gap-3">
  {/* Visa */}
<div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
  <img
    src="https://static.cdnlogo.com/logos/v/34/visa.svg"
    alt="Visa"
    className="h-6 w-auto"
  />
</div>

  {/* Mastercard */}
  <div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
    <img
      src="https://upload.wikimedia.org/wikipedia/commons/2/2a/Mastercard-logo.svg"
      alt="Mastercard"
      className="h-6"
    />
  </div>

  {/* PayPal */}
  <div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
    <img
      src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
      alt="PayPal"
      className="h-6"
    />
  </div>
</div>
                {/* Trust Badges */}
                <div className="space-y-3 sm:space-y-4">
                  <div className="bg-[#2a2a2a] p-3 sm:p-4 rounded-lg flex gap-3 border border-[#404040]">
                    <ShieldCheck size={18} className="text-emerald-500 flex-shrink-0 mt-1" />
                    <p className="text-[#AAAAAA] text-sm">
                      <span className="font-medium">100% Secure Checkout</span>
                      <br />All transactions are secured and encrypted
                    </p>
                  </div>
                  <div className="bg-[#2a2a2a] p-3 sm:p-4 rounded-lg flex gap-3 border border-[#404040]">
                    <Truck size={18} className="text-amber-500 flex-shrink-0 mt-1" />
                    <p className="text-[#AAAAAA] text-sm">
                      <span className="font-medium">Free Shipping</span>
                      <br />On all orders!!
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default CartPage;