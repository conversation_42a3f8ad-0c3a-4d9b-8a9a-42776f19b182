import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import viteCompression from 'vite-plugin-compression'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Gzip compression
    viteCompression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 1024,
      deleteOriginFile: false,
    }),
    // Brotli compression (better than gzip)
    viteCompression({
      algorithm: 'brotliCompress',
      ext: '.br',
      threshold: 1024,
      deleteOriginFile: false,
    }),
  ],

  // Build optimizations
  build: {
    // Enable minification
    minify: 'terser',

    // Terser options for aggressive optimization
    terserOptions: {
      compress: {
        // Remove console.log in production
        drop_console: true,
        drop_debugger: true,
        // Remove unused code
        dead_code: true,
        // Optimize conditionals
        conditionals: true,
        // Optimize comparisons
        comparisons: true,
        // Optimize sequences
        sequences: true,
        // Remove unused variables
        unused: true,
        // Join consecutive var statements
        join_vars: true,
        // Collapse single-use variables
        collapse_vars: true,
        // Reduce variables
        reduce_vars: true,
        // Optimize if-return and if-continue
        if_return: true,
        // Optimize loops
        loops: true,
      },
      mangle: {
        // Mangle variable names for smaller bundle
        toplevel: true,
      },
      format: {
        // Remove comments
        comments: false,
      },
    },

    // Rollup options for manual chunking
    rollupOptions: {
      output: {
        // Manual chunks for better caching
        manualChunks: {
          // Vendor chunk for React and related libraries
          vendor: ['react', 'react-dom', 'react-router-dom'],

          // UI libraries chunk
          ui: ['framer-motion', 'react-icons', 'lucide-react'],

          // Utility libraries chunk
          utils: ['clsx', 'tailwind-merge'],

          // Animation libraries chunk
          animations: ['react-spring', 'react-spring-3d-carousel'],

          // Other libraries chunk
          libs: ['swiper', 'react-range', 'react-share', 'react-toastify', 'react-helmet-async'],

          // Payment and external services
          services: ['razorpay'],
        },

        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop().replace('.js', '')
            : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },

        // Optimize asset file names
        assetFileNames: (assetInfo) => {
          const fileName = assetInfo.names?.[0] || 'asset';
          const info = fileName.split('.');
          const ext = info[info.length - 1];
          if (/\.(css)$/.test(fileName)) {
            return `css/[name]-[hash].${ext}`;
          }
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(fileName)) {
            return `images/[name]-[hash].${ext}`;
          }
          return `assets/[name]-[hash].${ext}`;
        },

        // Optimize entry file names
        entryFileNames: 'js/[name]-[hash].js',
      },
    },

    // Target modern browsers for better optimization
    target: 'es2020',

    // Optimize CSS
    cssCodeSplit: true,

    // Source maps for debugging (disable in production)
    sourcemap: false,

    // Chunk size warning limit
    chunkSizeWarningLimit: 1000,

    // Optimize assets
    assetsInlineLimit: 4096, // Inline assets smaller than 4kb
  },

  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'react-icons',
      'lucide-react',
      'clsx',
      'tailwind-merge',
      'react-spring',
      'swiper',
      'react-toastify',
    ],
    exclude: ['react-spring-3d-carousel'], // Exclude problematic deps
  },

  // Server configuration for development
  server: {
    // Enable compression in dev mode
    compress: true,
    // Optimize HMR
    hmr: {
      overlay: false,
    },
  },

  // Preview server configuration
  preview: {
    // Enable compression
    compress: true,
  },
})
