import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Search } from 'lucide-react';

import { optimizeImageUrl } from '../utils/imageOptimization';

// Swiper imports
import { Swiper, SwiperSlide } from 'swiper/react';
import { EffectFade, Autoplay, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/effect-fade';
import 'swiper/css/pagination';

// Static carousel products with Cloudinary URLs for instant loading
const carouselProducts = [
  {
    id: 22,
    name: "Premium Linen Shirt",
    price: 1099,
    category: "Shirts",
    image_url: "https://res.cloudinary.com/dxbocppgi/image/upload/v1752699108/ProductsImages/shirt1BL_1.webp",
    description: "Premium quality linen shirt with UV protection",
    isBestSeller: true,
    isNew: false
  },
  {
    id: 19,
    name: "Classic T-Shirt",
    price: 1499,
    category: "T-Shirts",
    image_url: "https://res.cloudinary.com/dxbocppgi/image/upload/v1752699115/ProductsImages/tshirt4_1.webp",
    description: "100% Cotton, 260 GSM, Regular fit",
    isBestSeller: true,
    isNew: false
  },
  {
    id: 5,
    name: "Oversized Tee",
    price: 1799,
    category: "Oversized",
    image_url: "https://res.cloudinary.com/dxbocppgi/image/upload/v1752699102/ProductsImages/oversized5_1.webp",
    description: "Oversized fit, Drop shoulder, Extra loose",
    isBestSeller: false,
    isNew: true
  },
  {
    id: 9,
    name: "Printed Shirt",
    price: 2299,
    category: "Shirts",
    image_url: "https://res.cloudinary.com/dxbocppgi/image/upload/v1752699099/ProductsImages/printedshirt1_1.webp",
    description: "100% cotton, Moisture-Absorbent, Lightweight",
    isBestSeller: false,
    isNew: true
  },
  {
    id: 1,
    name: "Linen Casual Shirt",
    price: 3299,
    category: "Shirts",
    image_url: "https://res.cloudinary.com/dxbocppgi/image/upload/v1752699106/ProductsImages/oversized1_1.webp",
    description: "100% Linen, Premium quality, UV protection",
    isBestSeller: true,
    isNew: false
  },
  {
    id: 15,
    name: "Graphic Tee",
    price: 1699,
    category: "T-Shirts",
    image_url: "https://res.cloudinary.com/dxbocppgi/image/upload/v1752699108/ProductsImages/oversized10_1.webp",
    description: "100% Cotton, Screen Print, Regular length",
    isBestSeller: false,
    isNew: true
  },
  {
    id: 17,
    name: "Premium Oversized",
    price: 2199,
    category: "Oversized",
    image_url: "https://res.cloudinary.com/dxbocppgi/image/upload/v1752699105/ProductsImages/oversized12W_4.webp",
    description: "Premium 260 GSM, Oversized fit, Drop shoulder",
    isBestSeller: true,
    isNew: false
  },
  {
    id: 20,
    name: "Cotton Polo",
    price: 1899,
    category: "Shirts",
    image_url: "https://res.cloudinary.com/dxbocppgi/image/upload/v1752699115/ProductsImages/tshirt5_1.webp",
    description: "Classic polo shirt, 100% cotton",
    isBestSeller: false,
    isNew: true
  }
];

// Custom hook for responsive design
const useResponsive = () => {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return { isMobile };
};

// Simple loading component
const SimpleLoader = ({ text = 'Loading...' }) => (
  <div className="flex items-center justify-center py-24">
    <div className="relative">
      <div className="w-12 h-12 border-4 border-[#FF6B35]/20 border-t-[#FF6B35] rounded-full animate-spin" />
      <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-orange-600 rounded-full animate-spin animate-reverse" style={{ animationDelay: '0.5s' }} />
    </div>
    {text && <span className="ml-4 text-gray-400 text-sm font-medium">{text}</span>}
  </div>
);

const Product3DHeroCarousel = ({ excludeProductId = null }) => {
  const navigate = useNavigate();
  const { isMobile } = useResponsive();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  // Handle search click - navigate to search page
  const handleSearchClick = () => {
    navigate('/search');
  };

  // Load static products for instant carousel display
  useEffect(() => {
    const loadStaticProducts = () => {
      setLoading(true);

      // Filter out excluded product from static array
      const filteredProducts = carouselProducts.filter(product => {
        const shouldExclude = excludeProductId && product.id === parseInt(excludeProductId);
        return !shouldExclude;
      });

      setFeaturedProducts(filteredProducts);
      setLoading(false);

      // Background preloading for better UX (non-blocking)
      setTimeout(() => {
        const preloadPromises = [];
        filteredProducts.forEach((product, index) => {
          if (product.image_url && product.image_url !== "YOUR_CLOUDINARY_URL_HERE") {
            const optimizedUrl = optimizeImageUrl(product.image_url, 600, 800, 'good');
            preloadPromises.push(new Promise((resolve) => {
              const img = new Image();
              img.onload = () => resolve();
              img.onerror = () => resolve();
              img.src = optimizedUrl;
              if ('fetchPriority' in img) {
                img.fetchPriority = index < 3 ? 'high' : 'low';
              }
              if ('decoding' in img) {
                img.decoding = 'async';
              }
            }));
          }
        });

        Promise.all(preloadPromises).then(() => {
          setImagesPreloaded(true);
        });
      }, 100);
    };

    loadStaticProducts();
  }, [excludeProductId]);

  // Preload images on hover for instant switching
  const handleImageHover = (product) => {
    if (product.images && product.images.length > 0) {
      product.images.forEach(imageUrl => {
        const optimizedUrl = optimizeImageUrl(imageUrl, 600, 800, 'good');
        const img = new Image();
        img.src = optimizedUrl;
      });
    }

    if (product.colors && product.colors.length > 0) {
      product.colors.forEach(color => {
        if (color.images && color.images.length > 0) {
          color.images.forEach(imageUrl => {
            const optimizedUrl = optimizeImageUrl(imageUrl, 600, 800, 'good');
            const img = new Image();
            img.src = optimizedUrl;
          });
        }
      });
    }
  };

  if (loading || !imagesPreloaded) {
    return (
      <section className="min-h-screen bg-black flex items-center justify-center">
        <SimpleLoader text="Loading WOLFFOXX Collection" />
      </section>
    );
  }

  if (featuredProducts.length === 0) {
    return (
      <section className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-['Bebas_Neue',sans-serif] text-white mb-4">
            WOLFFOXX
          </h2>
          <p className="text-gray-400">Collection loading...</p>
        </div>
      </section>
    );
  }

  return (
    <section className="min-h-screen bg-black relative flex items-center pt-4 md:pt-6 pb-0 md:pb-1">
      {/* Swiper Styles */}
      <style jsx>{`
        .swiper-pagination {
          bottom: 20px !important;
        }
        .swiper-pagination-bullet {
          width: 12px !important;
          height: 12px !important;
          background: #6b7280 !important;
          opacity: 0.6 !important;
          transition: all 0.3s ease !important;
        }
        .swiper-pagination-bullet-active {
          background: #FF8800 !important;
          opacity: 1 !important;
          transform: scale(1.25) !important;
          box-shadow: 0 0 10px rgba(255, 136, 0, 0.5) !important;
        }

        /* Desktop carousel styles */
        .desktop-carousel .swiper-wrapper {
          align-items: center;
        }

        .desktop-carousel .swiper-slide {
          display: flex;
          justify-content: center;
          align-items: center;
          transition: all 0.6s ease;
          transform: scale(0.7);
          opacity: 0.6;
          z-index: 1;
        }

        .desktop-carousel .swiper-slide-active {
          transform: scale(1) !important;
          opacity: 1 !important;
          z-index: 3 !important;
        }

        .desktop-carousel .swiper-slide-prev,
        .desktop-carousel .swiper-slide-next {
          transform: scale(0.75) !important;
          opacity: 0.8 !important;
          z-index: 2 !important;
        }

        /* Mobile styles */
        @media (max-width: 767px) {
          .mobile-carousel .swiper-slide {
            border-radius: 24px !important;
            overflow: hidden !important;
          }
        }
      `}</style>

      <div className="absolute inset-0 bg-black" />

      {/* Search Bar */}
      <motion.div
        className="absolute top-2 left-0 right-0 z-10"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="container mx-auto px-4 max-w-4xl">
          <div
            onClick={handleSearchClick}
            className="relative flex items-center bg-transparent backdrop-blur-sm rounded-lg border border-[#DDDDDD] overflow-hidden cursor-pointer hover:border-white transition-all duration-300 group"
          >
            <Search size={18} className="ml-3 text-[#f5f5f5] group-hover:text-white transition-colors" />
            <input
              type="text"
              placeholder="Search 'trouser'"
              className="w-full py-2 px-3 bg-transparent text-[#f5f5f5] placeholder:text-[#AAAAAA] border-none outline-none cursor-pointer text-sm"
              readOnly
            />
          </div>
        </div>
      </motion.div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 pt-6 sm:pt-7 md:pt-8">
{/* Hero Header - Perfect mobile, reduced desktop sizes */}
<div className="text-center mb-4 sm:mb-5 md:mb-7 lg:mb-9 px-2">
  <div className="mb-3 sm:mb-4 md:mb-5 lg:mb-7 pt-8 sm:pt-10 md:pt-12 lg:pt-16">
    <h1 className="font-black leading-none mb-3 sm:mb-4 md:mb-5 bg-gradient-to-r from-white to-[#FF8800] bg-clip-text text-transparent 
                 text-5xl xs:text-6xl sm:text-8xl md:text-6xl lg:text-7xl xl:text-[8rem]
                 tracking-tight xs:tracking-wide sm:tracking-wider md:tracking-widest
                 max-w-full overflow-hidden">
      WOLFFOXX
    </h1>
  </div>
</div>


        {/* Carousel Container */}
        <div className="relative mobile-carousel-height flex items-center justify-center mb-0 sm:mb-1 md:mb-2">
          {isMobile ? (
            /* Mobile: Fade Effect (UNCHANGED) */
            <div className="w-full h-full flex flex-col items-center justify-center px-4">
              <div className="w-full mobile-carousel-container mx-auto">
                <Swiper
                  modules={[EffectFade, Autoplay, Pagination]}
                  effect="fade"
                  fadeEffect={{
                    crossFade: true
                  }}
                  autoplay={{
                    delay: 3500,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: false
                  }}
                  speed={800}
                  loop={true}
                  allowTouchMove={true}
                  onSlideChange={(swiper) => setCurrentSlide(swiper.realIndex)}
                  className="w-full h-full mobile-carousel"
                >
                  {featuredProducts.map((product, index) => {
                    const primaryImage = product.image_url ||
                      product.images?.[0]?.url ||
                      product.images?.[0] ||
                      (product.colors && product.colors.length > 0 ? product.colors[0].images?.[0] : null);

                    const optimizedImage = primaryImage ?
                      optimizeImageUrl(primaryImage, 800, 1000, 'best') :
                      'https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=90&w=800&auto=format&fit=crop';

                    return (
                      <SwiperSlide key={product.id} className="flex items-center justify-center">
                        <div
                          className="carousel-slide cursor-pointer w-full h-full"
                          onClick={() => navigate(`/product/${product.id}`)}
                          onMouseEnter={() => handleImageHover(product)}
                        >
                          <div className="relative w-full h-full rounded-3xl overflow-hidden shadow-2xl group transition-all duration-1000 ease-out">
                            <div className="relative h-full overflow-hidden">
                              <img
                                src={optimizedImage}
                                alt={product.name || 'WOLFFOXX Product'}
                                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-1200 ease-out"
                                loading={index < 2 ? "eager" : "lazy"}
                                fetchPriority={index < 2 ? "high" : "low"}
                              />

                              {/* Product badges */}
                              <div className="absolute top-3 left-3 flex flex-col gap-1">
                                {product.is_new && (
                                  <span className="bg-green-500/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg">
                                    NEW
                                  </span>
                                )}
                                {product.is_bestseller && (
                                  <span className="bg-[#FF8800]/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg">
                                    BESTSELLER
                                  </span>
                                )}
                                {product.is_trending && (
                                  <span className="bg-purple-500/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg">
                                    TRENDING
                                  </span>
                                )}
                              </div>

                              {/* VIEW PRODUCT overlay */}
                              <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out">
                                <div className="bg-black/60 backdrop-blur-sm text-white px-3 py-1.5 rounded-lg font-medium text-sm transform translate-y-2 group-hover:translate-y-0 transition-transform duration-500 ease-out shadow-lg border border-white/20">
                                  VIEW PRODUCT
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </SwiperSlide>
                    );
                  })}
                </Swiper>
              </div>
            </div>
          ) : (
            /* Desktop: Traditional Carousel with Center Focus - NO NAVIGATION ARROWS */
            <div className="w-full h-full flex items-center justify-center">
              <div className="w-full max-w-7xl">
                <Swiper
                  modules={[Autoplay]}
                  grabCursor={true}
                  centeredSlides={true}
                  slidesPerView={3}
                  spaceBetween={30}
                  autoplay={{
                    delay: 3500,
                    disableOnInteraction: false,
                  }}
                  speed={800}
                  loop={true}
                  onSlideChange={(swiper) => setCurrentSlide(swiper.realIndex)}
                  className="desktop-carousel"
                  style={{ height: '650px', paddingBottom: '50px' }}
                  breakpoints={{
                    768: {
                      slidesPerView: 3,
                      spaceBetween: 30,
                    },
                    1024: {
                      slidesPerView: 3,
                      spaceBetween: 40,
                    },
                    1280: {
                      slidesPerView: 3,
                      spaceBetween: 50,
                    },
                  }}
                >
                  {featuredProducts.map((product, index) => {
                    const primaryImage = product.image_url ||
                      product.images?.[0]?.url ||
                      product.images?.[0] ||
                      (product.colors && product.colors.length > 0 ? product.colors[0].images?.[0] : null);

                    const optimizedImage = primaryImage ?
                      optimizeImageUrl(primaryImage, 800, 1000, 'best') :
                      'https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=90&w=800&auto=format&fit=crop';

                    return (
                      <SwiperSlide key={product.id}>
                        <div
                          className="carousel-slide cursor-pointer h-full w-full max-w-[400px] mx-auto"
                          onClick={() => navigate(`/product/${product.id}`)}
                          onMouseEnter={() => handleImageHover(product)}
                        >
                          <div className="relative w-full h-full rounded-2xl md:rounded-3xl overflow-hidden shadow-2xl group transition-all duration-700 ease-out hover:shadow-3xl">
                            <div className="relative h-full overflow-hidden">
                              <img
                                src={optimizedImage}
                                alt={product.name || 'WOLFFOXX Product'}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-1000 ease-out"
                                loading={index < 3 ? "eager" : "lazy"}
                                fetchPriority={index < 3 ? "high" : "low"}
                              />

                              <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease-out" />

                              {/* Product badges */}
                              <div className="absolute top-4 md:top-6 left-4 md:left-6 flex flex-col gap-2">
                                {product.is_new && (
                                  <span className="bg-green-500/90 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full font-bold shadow-lg">
                                    NEW
                                  </span>
                                )}
                                {product.is_bestseller && (
                                  <span className="bg-[#FF8800]/90 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full font-bold shadow-lg">
                                    BESTSELLER
                                  </span>
                                )}
                                {product.is_trending && (
                                  <span className="bg-purple-500/90 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full font-bold shadow-lg">
                                    TRENDING
                                  </span>
                                )}
                              </div>

                              {/* Product info overlay */}
                              {/* <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out transform translate-y-2 group-hover:translate-y-0">
                                <div className="bg-black/70 backdrop-blur-sm text-white p-3 rounded-lg border border-white/20">
                                  <h3 className="font-semibold text-sm mb-1 truncate">{product.name}</h3>
                                  <p className="text-[#FF8800] font-bold text-lg">₹{product.price?.toLocaleString()}</p>
                                </div>
                              </div> */}

                              {/* VIEW PRODUCT overlay */}
                              <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out">
                                <div className="bg-[#FF8800]/90 backdrop-blur-sm text-white px-4 py-2 rounded-lg font-medium text-sm transform translate-y-2 group-hover:translate-y-0 transition-transform duration-500 ease-out shadow-lg hover:bg-[#FF8800] cursor-pointer">
                                  VIEW PRODUCT
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </SwiperSlide>
                    );
                  })}
                </Swiper>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Product3DHeroCarousel;
