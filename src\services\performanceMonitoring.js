// Performance Monitoring Service
// Tracks Core Web Vitals, memory usage, and network performance

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      fcp: null, // First Contentful Paint
      lcp: null, // Largest Contentful Paint
      fid: null, // First Input Delay
      cls: null, // Cumulative Layout Shift
      ttfb: null, // Time to First Byte
      memory: null,
      networkInfo: null
    };
    
    this.observers = [];
    this.isMonitoring = false;
    this.init();
  }

  init() {
    if (typeof window === 'undefined') return;
    
    this.isMonitoring = true;
    this.setupCoreWebVitals();
    this.setupMemoryMonitoring();
    this.setupNetworkMonitoring();
    this.setupNavigationTiming();
    
    console.log('🔍 Performance monitoring initialized');
  }

  setupCoreWebVitals() {
    // First Contentful Paint (FCP)
    this.observePerformanceEntry('paint', (entries) => {
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.metrics.fcp = fcpEntry.startTime;
        this.logMetric('FCP', fcpEntry.startTime, 'ms');
      }
    });

    // Largest Contentful Paint (LCP)
    this.observePerformanceEntry('largest-contentful-paint', (entries) => {
      const lcpEntry = entries[entries.length - 1];
      if (lcpEntry) {
        this.metrics.lcp = lcpEntry.startTime;
        this.logMetric('LCP', lcpEntry.startTime, 'ms');
      }
    });

    // First Input Delay (FID)
    this.observePerformanceEntry('first-input', (entries) => {
      const fidEntry = entries[0];
      if (fidEntry) {
        this.metrics.fid = fidEntry.processingStart - fidEntry.startTime;
        this.logMetric('FID', this.metrics.fid, 'ms');
      }
    });

    // Cumulative Layout Shift (CLS)
    this.observePerformanceEntry('layout-shift', (entries) => {
      let clsValue = 0;
      for (const entry of entries) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      this.metrics.cls = clsValue;
      this.logMetric('CLS', clsValue, '');
    });
  }

  observePerformanceEntry(type, callback) {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          callback(list.getEntries());
        });
        observer.observe({ type, buffered: true });
        this.observers.push(observer);
      } catch (error) {
        console.warn(`Failed to observe ${type}:`, error);
      }
    }
  }

  setupMemoryMonitoring() {
    if ('memory' in performance) {
      const updateMemoryInfo = () => {
        this.metrics.memory = {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
          usedMB: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          totalMB: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
        };
      };

      updateMemoryInfo();
      setInterval(updateMemoryInfo, 30000); // Update every 30 seconds
    }
  }

  setupNetworkMonitoring() {
    if ('connection' in navigator) {
      this.metrics.networkInfo = {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt,
        saveData: navigator.connection.saveData
      };

      navigator.connection.addEventListener('change', () => {
        this.metrics.networkInfo = {
          effectiveType: navigator.connection.effectiveType,
          downlink: navigator.connection.downlink,
          rtt: navigator.connection.rtt,
          saveData: navigator.connection.saveData
        };
        console.log('📶 Network changed:', this.metrics.networkInfo);
      });
    }
  }

  setupNavigationTiming() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
          this.metrics.ttfb = navigation.responseStart - navigation.requestStart;
          this.logMetric('TTFB', this.metrics.ttfb, 'ms');
          
          // Log additional timing metrics
          const timings = {
            'DNS Lookup': navigation.domainLookupEnd - navigation.domainLookupStart,
            'TCP Connect': navigation.connectEnd - navigation.connectStart,
            'Request': navigation.responseStart - navigation.requestStart,
            'Response': navigation.responseEnd - navigation.responseStart,
            'DOM Processing': navigation.domContentLoadedEventStart - navigation.responseEnd,
            'Load Complete': navigation.loadEventEnd - navigation.loadEventStart
          };

          console.log('⏱️ Navigation Timings:', timings);
        }
      }, 0);
    });
  }

  logMetric(name, value, unit) {
    const formattedValue = typeof value === 'number' ? value.toFixed(2) : value;
    console.log(`📊 ${name}: ${formattedValue}${unit}`);
    
    // Warn if metrics are poor
    this.checkMetricThresholds(name, value);
  }

  checkMetricThresholds(name, value) {
    const thresholds = {
      FCP: { good: 1800, poor: 3000 },
      LCP: { good: 2500, poor: 4000 },
      FID: { good: 100, poor: 300 },
      CLS: { good: 0.1, poor: 0.25 },
      TTFB: { good: 800, poor: 1800 }
    };

    const threshold = thresholds[name];
    if (!threshold) return;

    if (value > threshold.poor) {
      console.warn(`⚠️ Poor ${name}: ${value} (threshold: ${threshold.poor})`);
    } else if (value > threshold.good) {
      console.log(`⚡ Needs improvement ${name}: ${value} (threshold: ${threshold.good})`);
    } else {
      console.log(`✅ Good ${name}: ${value}`);
    }
  }

  getMetrics() {
    return { ...this.metrics };
  }

  getPerformanceReport() {
    return {
      timestamp: new Date().toISOString(),
      metrics: this.getMetrics(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
  }

  // Send metrics to analytics (implement as needed)
  sendMetrics() {
    const report = this.getPerformanceReport();
    console.log('📈 Performance Report:', report);
    
    // TODO: Send to your analytics service
    // Example: analytics.track('performance_metrics', report);
  }

  // Monitor resource loading performance
  monitorResourceLoading() {
    this.observePerformanceEntry('resource', (entries) => {
      entries.forEach(entry => {
        const duration = entry.responseEnd - entry.startTime;
        if (duration > 1000) { // Warn for resources taking > 1s
          console.warn(`🐌 Slow resource: ${entry.name} (${duration.toFixed(2)}ms)`);
        }
      });
    });
  }

  // Monitor long tasks that block the main thread
  monitorLongTasks() {
    this.observePerformanceEntry('longtask', (entries) => {
      entries.forEach(entry => {
        console.warn(`⏳ Long task detected: ${entry.duration.toFixed(2)}ms`);
      });
    });
  }

  disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.isMonitoring = false;
    console.log('🔍 Performance monitoring disconnected');
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions
export const trackPageLoad = () => {
  performanceMonitor.sendMetrics();
};

export const trackUserInteraction = (action, element) => {
  console.log(`👆 User interaction: ${action} on ${element}`);
};

export const getPerformanceMetrics = () => {
  return performanceMonitor.getMetrics();
};

export default performanceMonitor;
