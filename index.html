<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./src/assets/logo40.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

    <!-- SEO Meta Tags -->
    <title>WOLFFOXX - Premium Fashion & Streetwear | Official Store</title>
    <meta name="description" content="Discover WOLFFOXX's premium collection of streetwear, t-shirts, oversized tees, and fashion essentials. Shop the latest trends with fast delivery and quality guarantee.">
    <meta name="keywords" content="WOLFFOXX, streetwear, fashion, t-shirts, oversized, premium clothing, online store, trendy fashion">
    <meta name="author" content="WOLFFOXX">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="WOLFFOXX - Premium Fashion & Streetwear">
    <meta property="og:description" content="Discover WOLFFOXX's premium collection of streetwear, t-shirts, oversized tees, and fashion essentials. Shop the latest trends with fast delivery.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://wolffoxx.com">
    <meta property="og:image" content="https://wolffoxx.com/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="WOLFFOXX">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="WOLFFOXX - Premium Fashion & Streetwear">
    <meta name="twitter:description" content="Discover WOLFFOXX's premium collection of streetwear, t-shirts, oversized tees, and fashion essentials.">
    <meta name="twitter:image" content="https://wolffoxx.com/twitter-image.jpg">
    <meta name="twitter:site" content="@wolffoxx">
    <meta name="twitter:creator" content="@wolffoxx">

    <!-- Additional SEO Meta Tags -->
    <meta name="theme-color" content="#000000">
    <meta name="msapplication-TileColor" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="WOLFFOXX">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://wolffoxx.com">

    <!-- Alternate Languages (if applicable) -->
    <link rel="alternate" hreflang="en" href="https://wolffoxx.com">

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "WOLFFOXX",
      "url": "https://wolffoxx.com",
      "logo": "https://wolffoxx.com/logo.png",
      "description": "Premium fashion and streetwear brand offering high-quality clothing and accessories.",
      "sameAs": [
        "https://www.instagram.com/wolffoxx",
        "https://www.facebook.com/wolffoxx",
        "https://twitter.com/wolffoxx"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+91-XXXXXXXXXX",
        "contactType": "customer service",
        "availableLanguage": "English"
      }
    }
    </script>

    <!-- Optimized Resource Hints for Performance -->
    <!-- High Priority: Critical resources for LCP -->
    <link rel="preconnect" href="https://res.cloudinary.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Medium Priority: Important but not critical -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//cdn.tailwindcss.com">

    <!-- Low Priority: Defer until after critical content -->
    <script>
      // Defer non-critical DNS prefetches
      window.addEventListener('load', function() {
        const lowPriorityDomains = [
          '//images.unsplash.com',
          '//checkout.razorpay.com',
          '//api.razorpay.com',
          '//wolffoxx.com'
        ];

        lowPriorityDomains.forEach(domain => {
          const link = document.createElement('link');
          link.rel = 'dns-prefetch';
          link.href = domain;
          document.head.appendChild(link);
        });
      });
    </script>

    <!-- Defer FontAwesome loading to prevent render blocking -->
    <script>
      // Load FontAwesome asynchronously after critical content
      window.addEventListener('load', function() {
        const fontAwesome = document.createElement('link');
        fontAwesome.rel = 'stylesheet';
        fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css';
        fontAwesome.integrity = 'sha512-yU8vQ7JIR0XytpgQyKIMpOwMbV+2IjVUZbi06g2a5c0ZpJZqtQyMuoyDCiwX+vYtCJj5YtqfqDaA4Klfy7L1eA==';
        fontAwesome.crossOrigin = 'anonymous';
        fontAwesome.referrerPolicy = 'no-referrer';
        document.head.appendChild(fontAwesome);
      });
    </script>
    <noscript>
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
        integrity="sha512-yU8vQ7JIR0XytpgQyKIMpOwMbV+2IjVUZbi06g2a5c0ZpJZqtQyMuoyDCiwX+vYtCJj5YtqfqDaA4Klfy7L1eA=="
        crossorigin="anonymous"
        referrerpolicy="no-referrer"
      />
    </noscript>

    <!-- Preload only critical API endpoints for homepage -->
    <script>
      // Conditionally preload API endpoints based on page
      if (window.location.pathname === '/' || window.location.pathname === '/home') {
        const criticalEndpoints = [
          '/api/v1/products?featured=true&per_page=4', // Reduced from 8 to 4
          '/api/v1/categories'
        ];

        criticalEndpoints.forEach(endpoint => {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'fetch';
          link.crossOrigin = 'anonymous';
          link.href = endpoint;
          document.head.appendChild(link);
        });
      }
    </script>

    <!-- Defer Tailwind CSS to prevent render blocking -->
    <script>
      // Load Tailwind CSS asynchronously after critical content
      window.addEventListener('DOMContentLoaded', function() {
        const tailwindScript = document.createElement('script');
        tailwindScript.src = 'https://cdn.tailwindcss.com';
        tailwindScript.onload = function() {
          tailwind.config = {
            darkMode: 'class',
            theme: {
              extend: {
                fontFamily: {
                  'sans': ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
                  'bebas': ['Bebas Neue', 'sans-serif'],
                },
                animation: {
                  'pulse-glow': 'pulse-glow 2s infinite',
                },
                keyframes: {
                  'pulse-glow': {
                    '0%, 100%': {
                      'box-shadow': '0 0 5px 0 rgba(249, 115, 22, 0.4)',
                    },
                    '50%': {
                      'box-shadow': '0 0 15px 0 rgba(249, 115, 22, 0.7)',
                    },
                  },
                },
              },
            },
          };
        };
        document.head.appendChild(tailwindScript);
      });
    </script>
    <style>
      /* Custom scrollbar for dark theme */
      ::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }

      ::-webkit-scrollbar-track {
        background-color: #1a1a1a;
      }

      ::-webkit-scrollbar-thumb {
        background-color: #404040;
        border-radius: 9999px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background-color: #6a6a6a;
      }

      /* Basic styles */
      html.dark {
        color-scheme: dark;
      }

      body {
        background-color: #000000;
        color: #d4d4d4;
        overflow-x: hidden;
        width: 100%;
        max-width: 100vw;
      }

      /* Prevent zoom issues */
      * {
        box-sizing: border-box;
      }

      html, body, #root {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
      }

      /* Product card animations */
      .product-card img {
        transition: transform 0.5s ease;
      }

      .product-card:hover img {
        transform: scale(1.05);
      }

      .product-card .arrow-button {
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
      }

      .product-card:hover .arrow-button {
        opacity: 1;
        transform: translateY(0);
      }

      .color-dot {
        transition: all 0.2s ease;
        border: 2px solid transparent;
      }

      .color-dot.active {
        transform: scale(1.25);
        border-color: #000;
      }

      /* Hide scrollbar utility */
      .scrollbar-hide {
        -ms-overflow-style: none;  /* Internet Explorer 10+ */
        scrollbar-width: none;  /* Firefox */
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;  /* Safari and Chrome */
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
