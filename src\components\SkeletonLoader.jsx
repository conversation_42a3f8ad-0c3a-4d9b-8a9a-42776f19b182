import React from 'react';

/**
 * Skeleton Loader Components to Prevent Layout Shift
 * These components maintain exact dimensions while content loads
 */

// Base skeleton component
export function Skeleton({ 
  width = '100%', 
  height = '20px', 
  className = '', 
  rounded = false,
  animate = true 
}) {
  return (
    <div
      className={`bg-gray-200 ${animate ? 'animate-pulse' : ''} ${rounded ? 'rounded' : ''} ${className}`}
      style={{
        width,
        height,
        minHeight: height
      }}
    />
  );
}

// Product Card Skeleton
export function ProductCardSkeleton({ className = '' }) {
  return (
    <div className={`bg-white rounded-lg overflow-hidden ${className}`}>
      {/* Image skeleton with fixed aspect ratio */}
      <div className="relative w-full" style={{ aspectRatio: '3/4' }}>
        <Skeleton width="100%" height="100%" className="absolute inset-0" />
      </div>
      
      {/* Content skeleton */}
      <div className="p-4 space-y-3">
        {/* Title skeleton */}
        <Skeleton width="80%" height="16px" />
        
        {/* Price skeleton */}
        <Skeleton width="40%" height="20px" />
        
        {/* Color dots skeleton */}
        <div className="flex gap-2">
          {[...Array(3)].map((_, i) => (
            <Skeleton 
              key={i} 
              width="20px" 
              height="20px" 
              rounded 
              className="rounded-full" 
            />
          ))}
        </div>
      </div>
    </div>
  );
}

// Hero Section Skeleton
export function HeroSkeleton({ className = '' }) {
  return (
    <div className={`relative w-full h-screen ${className}`}>
      {/* Background image skeleton */}
      <Skeleton width="100%" height="100%" className="absolute inset-0" />
      
      {/* Content overlay skeleton */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center space-y-6">
          {/* Title skeleton */}
          <Skeleton width="300px" height="48px" className="mx-auto" />
          
          {/* Subtitle skeleton */}
          <Skeleton width="200px" height="20px" className="mx-auto" />
          
          {/* Button skeleton */}
          <Skeleton width="120px" height="40px" className="mx-auto rounded" />
        </div>
      </div>
    </div>
  );
}

// Image Gallery Skeleton
export function ImageGallerySkeleton({ count = 4, className = '' }) {
  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 ${className}`}>
      {[...Array(count)].map((_, i) => (
        <div key={i} className="relative" style={{ aspectRatio: '1/1' }}>
          <Skeleton width="100%" height="100%" className="absolute inset-0 rounded" />
        </div>
      ))}
    </div>
  );
}

// Text Content Skeleton
export function TextSkeleton({ lines = 3, className = '' }) {
  return (
    <div className={`space-y-2 ${className}`}>
      {[...Array(lines)].map((_, i) => (
        <Skeleton 
          key={i} 
          width={i === lines - 1 ? '60%' : '100%'} 
          height="16px" 
        />
      ))}
    </div>
  );
}

// Navigation Skeleton
export function NavSkeleton({ className = '' }) {
  return (
    <div className={`flex items-center justify-between p-4 ${className}`}>
      {/* Logo skeleton */}
      <Skeleton width="120px" height="32px" />
      
      {/* Menu items skeleton */}
      <div className="hidden md:flex gap-6">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} width="60px" height="20px" />
        ))}
      </div>
      
      {/* Icons skeleton */}
      <div className="flex gap-4">
        {[...Array(3)].map((_, i) => (
          <Skeleton key={i} width="24px" height="24px" rounded className="rounded-full" />
        ))}
      </div>
    </div>
  );
}

// Footer Skeleton
export function FooterSkeleton({ className = '' }) {
  return (
    <div className={`bg-gray-900 text-white p-8 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="space-y-4">
            {/* Section title */}
            <Skeleton width="80px" height="20px" className="bg-gray-700" />
            
            {/* Links */}
            <div className="space-y-2">
              {[...Array(4)].map((_, j) => (
                <Skeleton key={j} width="60%" height="16px" className="bg-gray-700" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Loading Page Skeleton
export function PageSkeleton({ className = '' }) {
  return (
    <div className={`min-h-screen ${className}`}>
      <NavSkeleton />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page title */}
        <Skeleton width="200px" height="32px" className="mb-8" />
        
        {/* Content grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {[...Array(12)].map((_, i) => (
            <ProductCardSkeleton key={i} />
          ))}
        </div>
      </div>
      
      <FooterSkeleton />
    </div>
  );
}

export default Skeleton;
