// Centralized Image Optimization Utility
// Provides consistent image optimization across the application

// Enhanced LRU Cache for image URLs with larger capacity for better performance
class LRUCache {
  constructor(capacity = 1000) {
    this.capacity = capacity;
    this.cache = new Map();
  }

  get(key) {
    if (this.cache.has(key)) {
      // Move to end (most recently used)
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return undefined;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      // Update existing
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      // Remove least recently used (first item)
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  has(key) {
    return this.cache.has(key);
  }

  clear() {
    this.cache.clear();
  }

  size() {
    return this.cache.size;
  }
}

// Enhanced image cache with LRU eviction
const imageCache = new LRUCache(1000);

// Preloaded images cache to avoid duplicate preloading
const preloadedImages = new Set();

// Image loading queue for better performance
const imageLoadingQueue = new Map();

/**
 * Enhanced image optimization utility with caching for instant loading
 * @param {string} url - Original image URL
 * @param {number} width - Desired width
 * @param {number} height - Desired height  
 * @param {string} quality - Quality setting ('good', 'best', 'auto')
 * @returns {string} Optimized image URL
 */
export const optimizeImageUrl = (url, width = 800, height = 800, quality = 'good') => {
  if (!url) return 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';

  // Create cache key
  const cacheKey = `${url}-${width}-${height}-${quality}`;

  // Return cached URL if available
  if (imageCache.has(cacheKey)) {
    return imageCache.get(cacheKey);
  }

  let optimizedUrl;

  // If it's already a Cloudinary URL, optimize it with aggressive settings
  if (url.includes('cloudinary.com')) {
    // Super aggressive optimization parameters for maximum performance
    const transformations = [
      `w_${width}`,
      `h_${height}`,
      'c_limit',
      'f_auto', // Auto format (WebP, AVIF when supported)
      `q_auto:${quality}`, // Auto quality
      'fl_progressive', // Progressive loading
      'dpr_auto', // Auto device pixel ratio
      'fl_immutable_cache', // Better caching
      'fl_awebp', // Animated WebP support
      'fl_lossy', // Allow lossy compression for better size
      'fl_strip_profile', // Remove metadata for smaller size
      'fl_force_strip', // Force strip all metadata
    ].join(',');

    optimizedUrl = url.replace('/upload/', `/upload/${transformations}/`);
  }
  // If it's an Unsplash URL, optimize it
  else if (url.includes('unsplash.com')) {
    optimizedUrl = `${url}&w=${width}&h=${height}&fit=crop&auto=format&q=85`;
  }
  // If it's a GitHub raw URL, use it as is (already optimized)
  else if (url.includes('githubusercontent.com')) {
    optimizedUrl = url;
  }
  // Return original URL if not recognized
  else {
    optimizedUrl = url;
  }

  // Cache the optimized URL
  imageCache.set(cacheKey, optimizedUrl);
  return optimizedUrl;
};

/**
 * Get optimized image URL for different contexts
 * @param {string} url - Original image URL
 * @param {string} context - Context ('hero', 'card', 'thumbnail', 'zoom')
 * @returns {string} Optimized image URL
 */
export const getContextOptimizedUrl = (url, context = 'card') => {
  const contextSettings = {
    hero: { width: 800, height: 1000, quality: 'good' },
    card: { width: 400, height: 500, quality: 'good' },
    thumbnail: { width: 200, height: 250, quality: 'auto' },
    zoom: { width: 1200, height: 1500, quality: 'best' }
  };

  const settings = contextSettings[context] || contextSettings.card;
  return optimizeImageUrl(url, settings.width, settings.height, settings.quality);
};

/**
 * Enhanced preload image with deduplication and priority
 * @param {string} url - Image URL to preload
 * @param {string} priority - Loading priority ('high', 'low', 'auto')
 * @returns {Promise} Promise that resolves when image is loaded
 */
export const preloadImage = (url, priority = 'auto') => {
  // Avoid duplicate preloading
  if (preloadedImages.has(url)) {
    return Promise.resolve();
  }

  // Check if already loading
  if (imageLoadingQueue.has(url)) {
    return imageLoadingQueue.get(url);
  }

  const loadPromise = new Promise((resolve, reject) => {
    const img = new Image();

    // Set loading attributes for better performance
    if ('fetchPriority' in img) {
      img.fetchPriority = priority;
    }
    if ('decoding' in img) {
      img.decoding = 'async';
    }
    if ('loading' in img) {
      img.loading = priority === 'high' ? 'eager' : 'lazy';
    }

    img.onload = () => {
      preloadedImages.add(url);
      imageLoadingQueue.delete(url);
      resolve(img);
    };

    img.onerror = () => {
      imageLoadingQueue.delete(url);
      reject(new Error(`Failed to load image: ${url}`));
    };

    img.src = url;
  });

  imageLoadingQueue.set(url, loadPromise);
  return loadPromise;
};

/**
 * Generate srcset for responsive images
 * @param {string} url - Base image URL
 * @param {Array} sizes - Array of sizes [{width, height}, ...]
 * @returns {string} Srcset string
 */
export const generateSrcSet = (url, sizes = []) => {
  if (!url || sizes.length === 0) return '';
  
  return sizes
    .map(size => `${optimizeImageUrl(url, size.width, size.height)} ${size.width}w`)
    .join(', ');
};

/**
 * Get placeholder image URL
 * @param {number} width - Width of placeholder
 * @param {number} height - Height of placeholder
 * @param {string} text - Placeholder text
 * @returns {string} Placeholder URL
 */
export const getPlaceholderUrl = (width = 400, height = 400, text = 'Loading...') => {
  return `https://via.placeholder.com/${width}x${height}/1a1a1a/ffffff?text=${encodeURIComponent(text)}`;
};

/**
 * Intersection Observer for lazy loading images
 */
class LazyImageObserver {
  constructor() {
    this.observer = null;
    this.init();
  }

  init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target;
              const src = img.dataset.src;
              const srcset = img.dataset.srcset;

              if (src) {
                img.src = src;
                img.removeAttribute('data-src');
              }

              if (srcset) {
                img.srcset = srcset;
                img.removeAttribute('data-srcset');
              }

              img.classList.remove('lazy-loading');
              img.classList.add('lazy-loaded');

              this.observer.unobserve(img);
            }
          });
        },
        {
          rootMargin: '50px 0px', // Start loading 50px before entering viewport
          threshold: 0.01,
        }
      );
    }
  }

  observe(element) {
    if (this.observer && element) {
      this.observer.observe(element);
    }
  }

  unobserve(element) {
    if (this.observer && element) {
      this.observer.unobserve(element);
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// Global lazy image observer instance
export const lazyImageObserver = new LazyImageObserver();

/**
 * Setup lazy loading for an image element
 * @param {HTMLImageElement} img - Image element
 * @param {string} src - Image source URL
 * @param {string} srcset - Image srcset
 */
export const setupLazyLoading = (img, src, srcset = '') => {
  if (!img) return;

  img.dataset.src = src;
  if (srcset) {
    img.dataset.srcset = srcset;
  }

  img.classList.add('lazy-loading');
  img.src = getPlaceholderUrl(400, 400, 'Loading...');

  lazyImageObserver.observe(img);
};

/**
 * Batch preload multiple images with priority
 * @param {Array} urls - Array of image URLs
 * @param {string} priority - Loading priority
 * @returns {Promise} Promise that resolves when all images are loaded
 */
export const batchPreloadImages = async (urls, priority = 'auto') => {
  const batchSize = 5; // Process 5 images at a time
  const results = [];

  for (let i = 0; i < urls.length; i += batchSize) {
    const batch = urls.slice(i, i + batchSize);
    const batchPromises = batch.map(url =>
      preloadImage(url, priority).catch(() => null) // Don't fail entire batch on single error
    );

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
  }

  return results;
};

/**
 * Clear image caches (useful for memory management)
 */
export const clearImageCaches = () => {
  imageCache.clear();
  preloadedImages.clear();
  imageLoadingQueue.clear();
};

/**
 * Get cache statistics for debugging
 */
export const getCacheStats = () => {
  return {
    imageCacheSize: imageCache.size(),
    preloadedImagesCount: preloadedImages.size,
    loadingQueueSize: imageLoadingQueue.size,
  };
};

export default {
  optimizeImageUrl,
  getContextOptimizedUrl,
  preloadImage,
  generateSrcSet,
  getPlaceholderUrl,
  setupLazyLoading,
  batchPreloadImages,
  clearImageCaches,
  getCacheStats,
  lazyImageObserver
};
