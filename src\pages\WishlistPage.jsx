import React, { useState, useEffect } from 'react';
import { useWishlist } from '../context/WishlistContext';
import { Star, Search, Heart } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { reviewAPI } from '../services/reviewAPI';

// Global image cache for instant loading across wishlist page
const wishlistImageCache = new Map();
const wishlistPreloadedImages = new Set();

// Enhanced image optimization utility for wishlist page
const optimizeWishlistImageUrl = (url, width = 800, height = 800, quality = 'good') => {
  if (!url) return 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';

  const cacheKey = `${url}-${width}-${height}-${quality}`;

  if (wishlistImageCache.has(cacheKey)) {
    return wishlistImageCache.get(cacheKey);
  }

  let optimizedUrl;

  if (url.includes('cloudinary.com')) {
    optimizedUrl = url.replace('/upload/', `/upload/w_${width},h_${height},c_limit,f_webp,q_auto:${quality},fl_progressive,dpr_auto,fl_immutable_cache,fl_awebp/`);
  } else if (url.includes('unsplash.com')) {
    optimizedUrl = `${url}&w=${width}&h=${height}&fit=crop&auto=format&q=85`;
  } else {
    optimizedUrl = url;
  }

  wishlistImageCache.set(cacheKey, optimizedUrl);
  return optimizedUrl;
};

// SUPER AGGRESSIVE image preloading function for wishlist page
const preloadWishlistImages = (items) => {
  if (!items || items.length === 0) return;

  const preloadPromises = [];

  items.forEach((item, index) => {
    // Preload ONLY first 3 wishlist item images for fast LCP
    if (index < 3 && item.image) {
      const optimizedUrl = optimizeWishlistImageUrl(item.image, 800, 1000, 'good');
      if (!wishlistPreloadedImages.has(optimizedUrl)) {
        wishlistPreloadedImages.add(optimizedUrl);
        preloadPromises.push(new Promise((resolve) => {
          const img = new Image();
          img.onload = () => resolve();
          img.onerror = () => resolve();
          img.src = optimizedUrl;
          // Enable faster loading with fetch priority
          if ('fetchPriority' in img) {
            img.fetchPriority = 'high';
          }
          if ('decoding' in img) {
            img.decoding = 'async';
          }
          if ('loading' in img) {
            img.loading = 'eager';
          }
        }));
      }
    }

    // Preload additional images if available
    if (item.images && item.images.length > 0) {
      item.images.forEach(imageUrl => {
        const optimizedUrl = optimizeWishlistImageUrl(imageUrl, 800, 1000, 'good');
        if (!wishlistPreloadedImages.has(optimizedUrl)) {
          wishlistPreloadedImages.add(optimizedUrl);
          preloadPromises.push(new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve();
            img.onerror = () => resolve();
            img.src = optimizedUrl;
          }));
        }
      });
    }

    // Preload color variant images for instant color switching
    if (item.colors && item.colors.length > 0) {
      item.colors.forEach(color => {
        if (color.images && color.images.length > 0) {
          color.images.forEach(imageUrl => {
            const optimizedUrl = optimizeWishlistImageUrl(imageUrl, 800, 1000, 'good');
            if (!wishlistPreloadedImages.has(optimizedUrl)) {
              wishlistPreloadedImages.add(optimizedUrl);
              preloadPromises.push(new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve();
                img.onerror = () => resolve();
                img.src = optimizedUrl;
              }));
            }
          });
        }
      });
    }
  });

  // Wait for critical images to load
  return Promise.all(preloadPromises);
};

export default function WishlistPage() {
  const { wishlistItems, removeFromWishlist } = useWishlist();
  const navigate = useNavigate();
  const [reviewStats, setReviewStats] = useState({});
  const [loadingStats, setLoadingStats] = useState(true);
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  // SUPER AGGRESSIVE preloading for wishlist items
  useEffect(() => {
    if (wishlistItems.length === 0) return;

    const preloadAllImages = async () => {
      try {
        // Preload all wishlist item images
        await preloadWishlistImages(wishlistItems);
        setImagesPreloaded(true);
      } catch (error) {
        console.error('Failed to preload wishlist images:', error);
        setImagesPreloaded(true); // Continue even if preloading fails
      }
    };

    preloadAllImages();
  }, [wishlistItems]);

  // Load review statistics for all wishlist items
  useEffect(() => {
    const loadReviewStats = async () => {
      if (wishlistItems.length === 0) {
        setLoadingStats(false);
        return;
      }

      try {
        setLoadingStats(true);
        const productIds = wishlistItems.map(item => item.id);
        const stats = await reviewAPI.getMultipleProductReviewStats(productIds);
        setReviewStats(stats);
      } catch (error) {
        console.error('Failed to load review stats:', error);
      } finally {
        setLoadingStats(false);
      }
    };

    loadReviewStats();
  }, [wishlistItems]);

  const navigateToProduct = (productId) => {
    navigate(`/product/${productId}`);
  };

  if (wishlistItems.length === 0) {
    return (
      <div className="container mx-auto px-6 py-12">
        <div className="text-center">
          <div className="w-24 h-24 bg-gradient-to-br from-gray-800 to-gray-900 rounded-full mx-auto flex items-center justify-center mb-6">
            <Heart size={32} className="text-gray-600" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-4">Your Wishlist is Empty</h2>
          <p className="text-gray-400 mb-8">Start exploring our products and save your favorites!</p>
          <button
            onClick={() => navigate('/search')}
            className="px-8 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white rounded-xl font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 hover:scale-105"
          >
            Browse Products
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-6 py-12">
      <h1 className="text-3xl font-bold text-white mb-8 flex items-center gap-3">
        My Wishlist
        <span className="text-blue-400">({wishlistItems.length})</span>
      </h1>

      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {wishlistItems.map((item) => (
          <div
            key={item.id}
            className="group bg-gradient-to-br from-gray-900/60 to-gray-800/40 hover:from-gray-800/80 hover:to-gray-700/60 rounded-xl border border-gray-700/50 hover:border-gray-600/50 transition-all duration-300 hover:scale-[1.02] overflow-hidden"
          >
            <div className="relative aspect-[4/5] overflow-hidden">
              <img
                src={optimizeWishlistImageUrl(item.image, 800, 1000, 'good')}
                alt={item.name}
                className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
                loading="eager"
                fetchPriority="high"
                onError={(e) => {
                  console.log('Wishlist image failed to load:', e.target.src);
                  e.target.src = 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';
                }}
              />
              {item.isNew && (
                <div className="absolute top-4 left-4 bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded">
                  NEW
                </div>
              )}
            </div>

            <div className="p-4">
              <div className="flex items-start justify-between gap-2 mb-2">
                <h3 className="text-white font-semibold truncate group-hover:text-blue-400 transition-colors">
                  {item.name}
                </h3>
                <div className="flex items-center gap-1 bg-gray-800/50 px-2 py-1 rounded">
                  <Star size={12} className="text-yellow-400 fill-yellow-400" />
                  <span className="text-white text-sm">
                    {loadingStats ? (
                      <div className="w-8 h-3 bg-gray-700 animate-pulse rounded"></div>
                    ) : (
                      reviewStats[item.id]?.average_rating && reviewStats[item.id].average_rating > 0
                        ? `${reviewStats[item.id].average_rating.toFixed(1)} (${reviewStats[item.id].total_reviews})`
                        : 'No reviews'
                    )}
                  </span>
                </div>
              </div>

              <p className="text-gray-400 text-sm mb-3 truncate">{item.description}</p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {item.salePrice ? (
                    <>
                      <span className="text-gray-400 line-through">₹{item.price}</span>
                      <span className="text-white font-semibold">₹{item.salePrice}</span>
                    </>
                  ) : (
                    <span className="text-white font-semibold">₹{item.price}</span>
                  )}
                </div>
              </div>

              <div className="mt-3 grid grid-cols-[1fr,auto] gap-2">
                <button
                  onClick={() => navigateToProduct(item.id)}
                  className="px-4 py-2 bg-[#FF6B35]  text-white rounded-lg transition-colors text-sm font-medium flex items-center justify-center gap-2"
                >
                  <Search size={16} />
                  View Product
                </button>
                <button
                  onClick={() => removeFromWishlist(item.id)}
                  className="p-2 text-red-500 hover:text-white bg-red-500/10 hover:bg-red-500 rounded-lg transition-colors"
                >
                  <Heart size={18} fill="currentColor" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
