import React, { useState, useEffect, useRef, Suspense, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, useScroll, useTransform, useSpring, AnimatePresence, useMotionValue, useVelocity } from 'framer-motion';
import { ArrowRight, ChevronDown, Star,  ArrowUpRight,  Heart,  Zap, Shield, Truck, Award, Plus, Crown,ChevronLeft, ChevronRight} from 'lucide-react';
import { dataService } from '../services/dataService';
import WishlistButton from '../components/WishlistButton';
import SEOHead, { SEOPresets } from '../components/SEOHead.jsx';
import EnhancedStatsSection from './EnhancedStatsSection';
import Product3DHeroCarousel from '../components/Product3DCarousel';
// import Logo3D from '../components/Logo3D';

// const MarqueeText = React.lazy(() => import('../components/MarqueeText')); // Commented out - not used
const ColorPreviewSlider = React.lazy(() => import('../components/ColorPreviewSlider'));

// Image cache for instant loading
const imageCache = new Map();

// Enhanced image optimization utility with caching for instant loading
const optimizeImageUrl = (url, width = 800, height = 800, quality = 'best') => {
  if (!url) return 'https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=80&w=800&auto=format&fit=crop&ixlib=rb-4.0.3';

  // Create cache key
  const cacheKey = `${url}-${width}-${height}-${quality}`;

  // Return cached URL if available
  if (imageCache.has(cacheKey)) {
    return imageCache.get(cacheKey);
  }

  let optimizedUrl;

  // If it's already a Cloudinary URL, optimize it with high quality settings
  if (url.includes('cloudinary.com')) {
    // Enhanced optimization parameters matching ProductPage quality
    optimizedUrl = url.replace('/upload/', `/upload/w_${width},h_${height},c_limit,f_webp,q_auto:${quality},fl_progressive,dpr_auto,fl_immutable_cache,fl_awebp/`);
  }
  // If it's an Unsplash URL, optimize it
  else if (url.includes('unsplash.com')) {
    optimizedUrl = `${url}&w=${width}&h=${height}&fit=crop&auto=format&q=90`;
  }
  // Return original URL if not recognized
  else {
    optimizedUrl = url;
  }

  // Cache the optimized URL
  imageCache.set(cacheKey, optimizedUrl);
  return optimizedUrl;
};

// Enhanced Loading Components
const PremiumLoader = ({ height = 'py-12', text = 'Loading...', variant = 'spin' }) => (
  <div className={`${height} flex items-center justify-center`}>
    {variant === 'spin' ? (
      <div className="relative">
        <div className="w-12 h-12 border-4 border-blue-600/20 border-t-blue-600 rounded-full animate-spin" />
        <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-cyan-600 rounded-full animate-spin animate-reverse" style={{ animationDelay: '0.5s' }} />
      </div>
    ) : (
      <div className="flex space-x-2">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className="w-3 h-3 bg-blue-600 rounded-full animate-bounce"
            style={{ animationDelay: `${i * 0.2}s` }}
          />
        ))}
      </div>
    )}
    <span className="ml-4 text-gray-400 text-sm font-medium">{text}</span>
  </div>
);

// Auto-Scrolling Hero Banners Component
// const AutoScrollingHeroBanners = ({ onSearchClick }) => {
//   const [currentBanner, setCurrentBanner] = useState(0);
//   const [isAutoScrolling, setIsAutoScrolling] = useState(true);
//   const [isDragging, setIsDragging] = useState(false);
//   const scrollContainerRef = useRef(null);

//   // Hero banner data - Different themes, not all sale
//   const banners = [
//     {
//       id: 1,
//       title: "SUMMER",
//       subtitle: "SALE",
//       discount: "50",
//       description: "Up to 50% off summer styles",
//       buttonText: "SHOP SALE",
//       background: "#000000",
//       textColor: "white",
//       image: "https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=1200&auto=format&fit=crop",
//       style: "sale",
//       type: "sale"
//     },
//     {
//       id: 2,
//       title: "DAILY DOSE",
//       subtitle: "WARDROBE ESSENTIALS",
//       description: "Discover your everyday style",
//       buttonText: "SHOP NOW",
//       background: "#000000",
//       textColor: "white",
//       image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=1200&auto=format&fit=crop",
//       style: "light",
//       type: "collection"
//     },
//     {
//       id: 3,
//       title: "NEW",
//       subtitle: "ARRIVALS",
//       description: "Fresh styles just dropped",
//       buttonText: "DISCOVER",
//       background: "#000000",
//       textColor: "white",
//       image: "/src/assets/2148194056.jpg",
//       style: "modern",
//       type: "new"
//     }
//   ];

//   // Auto-scroll functionality
//   useEffect(() => {
//     if (!isAutoScrolling || isDragging) return;

//     const interval = setInterval(() => {
//       setCurrentBanner((prev) => (prev + 1) % banners.length);
//     }, 5000); // Change banner every 5 seconds

//     return () => clearInterval(interval);
//   }, [isAutoScrolling, isDragging, banners.length]);

//   const handleBannerChange = (index) => {
//     setCurrentBanner(index);
//     setIsAutoScrolling(false);
//     // Resume auto-scrolling after 10 seconds
//     setTimeout(() => setIsAutoScrolling(true), 10000);
//   };

//   const goToPrevious = () => {
//     setCurrentBanner((prev) => (prev - 1 + banners.length) % banners.length);
//     setIsAutoScrolling(false);
//     setTimeout(() => setIsAutoScrolling(true), 10000);
//   };

//   const goToNext = () => {
//     setCurrentBanner((prev) => (prev + 1) % banners.length);
//     setIsAutoScrolling(false);
//     setTimeout(() => setIsAutoScrolling(true), 10000);
//   };

//   // Touch/swipe handlers for mobile
//   const handleTouchStart = (e) => {
//     setIsDragging(true);
//     setIsAutoScrolling(false);
//   };

//   const handleTouchEnd = () => {
//     setIsDragging(false);
//     setTimeout(() => setIsAutoScrolling(true), 10000);
//   };

//   return (
//     <section className="relative h-screen overflow-hidden bg-black" style={{ minHeight: '100vh', minHeight: '100dvh' }}>
//       {/* Fixed Search Bar - Always visible at top */}
//       <motion.div
//         className="absolute top-0 left-0 right-0 z-30 pt-2 pb-3 md:pb-4"
//         initial={{ opacity: 0, y: -10 }}
//         animate={{ opacity: 1, y: 0 }}
//         transition={{ duration: 0.6, delay: 0.2 }}
//       >
//         <div className="container mx-auto px-3 md:px-4 max-w-4xl">
//           <div
//             onClick={onSearchClick}
//             className="relative flex items-center bg-transparent backdrop-blur-sm rounded-lg border border-[#DDDDDD] overflow-hidden cursor-pointer hover:border-white transition-all duration-300 group"
//           >
//             <Search size={16} className="ml-2 md:ml-3 text-[#f5f5f5] group-hover:text-white transition-colors" />
//             <input
//               type="text"
//               placeholder="Search 'trouser'"
//               className="w-full py-2 px-2 md:px-3 bg-transparent text-[#f5f5f5] placeholder:text-[#AAAAAA] border-none outline-none cursor-pointer text-sm"
//               readOnly
//             />
//           </div>
//         </div>
//       </motion.div>

//       {/* Horizontal Scrolling Container */}
//       <div
//         className="relative h-full overflow-hidden"
//         onTouchStart={handleTouchStart}
//         onTouchEnd={handleTouchEnd}
//         style={{
//           // Improve mobile scrolling performance
//           WebkitOverflowScrolling: 'touch',
//           touchAction: 'pan-x'
//         }}
//       >
//         <motion.div
//           ref={scrollContainerRef}
//           className="flex h-full"
//           animate={{ x: `-${currentBanner * 100}%` }}
//           transition={{ duration: 0.8, ease: "easeInOut" }}
//         >
//           {banners.map((banner, index) => (
//             <div
//               key={banner.id}
//               className="min-w-full h-full relative flex items-center"
//               style={{ background: banner.background }}
//             >
//               {/* Background Image */}
//               <div className="absolute inset-0">
//                 <img
//                   src={optimizeImageUrl(banner.image, 1920, 1080, 'best')}
//                   alt={`${banner.title} ${banner.subtitle}`}
//                   className="w-full h-full object-cover"
//                 />
//                 <div className="absolute inset-0 bg-black/70" />
//               </div>

//               {/* Banner Content */}
//               <div className="relative z-10 container mx-auto px-3 md:px-4 pt-16 md:pt-20">
//                 <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 items-center h-full">
//                   {/* Text Content */}
//                   <div className="text-center lg:text-left">
//                     <div className="mb-4 md:mb-6">
//                       <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black tracking-tight mb-2 leading-tight">
//                         <span
//                           className="block bg-gradient-to-r from-[#FF6F35] via-[#FF8A65] to-[#FFB74D] bg-clip-text text-transparent"
//                           style={{
//                             WebkitBackgroundClip: 'text',
//                             WebkitTextFillColor: 'transparent',
//                             backgroundClip: 'text'
//                           }}
//                         >
//                           {banner.title}
//                         </span>
//                         <span className="block text-white">
//                           {banner.subtitle}
//                         </span>
//                       </h1>
//                     </div>

//                     <div className="mb-6 md:mb-8">
//                       {banner.type === 'sale' && banner.discount && (
//                         <div className="flex items-center justify-center lg:justify-start gap-2 md:gap-4 mb-3 md:mb-4">
//                           <span className="text-3xl sm:text-4xl md:text-5xl font-bold text-white">
//                             UP TO {banner.discount}%
//                           </span>
//                           <span className="text-xl sm:text-2xl md:text-2xl font-light text-white/80">OFF</span>
//                         </div>
//                       )}
//                       <p className="text-lg sm:text-xl md:text-xl text-white/80 mb-6 md:mb-8 max-w-md mx-auto lg:mx-0">
//                         {banner.description}
//                       </p>
//                     </div>

//                     <div>
//                       <Link
//                         to="/products"
//                         className="inline-flex items-center gap-2 md:gap-3 bg-[#FF6F35] hover:bg-[#FF5722] text-white px-6 md:px-8 py-3 md:py-4 rounded-lg font-semibold text-base md:text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
//                       >
//                         {banner.buttonText}
//                         <ArrowRight size={18} className="md:w-5 md:h-5" />
//                       </Link>
//                     </div>
//                   </div>

//                   {/* Right side - Additional visual element */}
//                   <div className="hidden lg:block">
//                     <div className="relative">
//                       <div className="w-64 h-64 bg-gradient-to-br from-[#FF6F35]/20 to-[#FF8A65]/20 rounded-full blur-3xl" />
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </motion.div>

//         {/* Always Visible Navigation Arrows - Desktop */}
//         <button
//           onClick={goToPrevious}
//           className="hidden md:flex absolute left-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full items-center justify-center text-white hover:bg-white/20 transition-all duration-300"
//         >
//           <ChevronLeft size={24} />
//         </button>

//         <button
//           onClick={goToNext}
//           className="hidden md:flex absolute right-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full items-center justify-center text-white hover:bg-white/20 transition-all duration-300"
//         >
//           <ChevronRight size={24} />
//         </button>

//         {/* Mobile Navigation Arrows - Smaller and positioned better */}
//         <button
//           onClick={goToPrevious}
//           className="md:hidden absolute left-2 top-1/2 transform -translate-y-1/2 z-20 w-10 h-10 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center text-white active:bg-white/30 transition-all duration-300"
//         >
//           <ChevronLeft size={20} />
//         </button>

//         <button
//           onClick={goToNext}
//           className="md:hidden absolute right-2 top-1/2 transform -translate-y-1/2 z-20 w-10 h-10 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center text-white active:bg-white/30 transition-all duration-300"
//         >
//           <ChevronRight size={20} />
//         </button>

//         {/* Banner Navigation Dots - Responsive */}
//         <div className="absolute bottom-6 md:bottom-8 left-1/2 transform -translate-x-1/2 z-20">
//           <div className="flex gap-2 md:gap-3">
//             {banners.map((_, index) => (
//               <button
//                 key={index}
//                 onClick={() => handleBannerChange(index)}
//                 className={`w-2.5 h-2.5 md:w-3 md:h-3 rounded-full transition-all duration-300 ${
//                   index === currentBanner
//                     ? 'bg-[#FF6F35] scale-125'
//                     : 'bg-white/40 hover:bg-white/60 active:bg-white/80'
//                 }`}
//               />
//             ))}
//           </div>
//         </div>

//         {/* Progress Bar */}
//         <div className="absolute bottom-0 left-0 w-full h-1 bg-white/20 z-20">
//           <motion.div
//             className="h-full bg-[#FF6F35]"
//             initial={{ width: "0%" }}
//             animate={{ width: "100%" }}
//             transition={{ duration: 5, ease: "linear" }}
//             key={currentBanner}
//           />
//         </div>
//       </div>
//     </section>
//   );
// };

// Floating Elements
const FloatingElements = () => {
  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2 bg-blue-500/20 rounded-full"
          animate={{
            y: [0, -120, 0],
            x: [0, Math.random() * 120 - 60, 0],
            opacity: [0, 0.8, 0],
          }}
          transition={{
            duration: 4 + Math.random() * 3,
            repeat: Infinity,
            delay: i * 0.7,
          }}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${100 + Math.random() * 100}%`,
          }}
        />
      ))}
    </div>
  );
};

export default function HomePage() {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isVideoMuted, setIsVideoMuted] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });


  // Scroll indicators state
  const [bestSellerScrollIndex, setBestSellerScrollIndex] = useState(0);

  // Navigation hook for search redirection
  const navigate = useNavigate();

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  const containerRef = useRef(null);
  const heroVideoRef = useRef(null);
  // const lookbookRef = useRef(null); // COMMENTED OUT - Lookbook section disabled
  const featuredRef = useRef(null);
  const bestSellerScrollRef = useRef(null);

  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 300], [0, -50]);
  const y2 = useTransform(scrollY, [0, 300], [0, 100]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0]);

  // Enhanced parallax effects for different sections
  const parallaxY1 = useTransform(scrollY, [0, 1000], [0, -100]);
  const parallaxY2 = useTransform(scrollY, [0, 1000], [0, 50]);
  const parallaxY3 = useTransform(scrollY, [0, 1500], [0, -75]);
  const scaleEffect = useTransform(scrollY, [0, 500], [1, 1.1]);
  const rotateEffect = useTransform(scrollY, [0, 1000], [0, 5]);

  // Mouse tracking for magnetic effects
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const mouseXSpring = useSpring(mouseX);
  const mouseYSpring = useSpring(mouseY);

  // Scroll handlers for indicators
  const handleBestSellerScroll = () => {
    if (bestSellerScrollRef.current) {
      const container = bestSellerScrollRef.current;
      const scrollLeft = container.scrollLeft;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16; // gap-4
      const totalItemWidth = itemWidth + gap;
      const index = Math.round(scrollLeft / totalItemWidth);
      setBestSellerScrollIndex(Math.min(index, featuredProducts.length - 1));
    }
  };





  // Scroll to item functions
  const scrollToBestSeller = (index) => {
    if (bestSellerScrollRef.current) {
      const container = bestSellerScrollRef.current;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16;
      const totalItemWidth = itemWidth + gap;
      const scrollLeft = index * totalItemWidth;
      container.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    }
  };





  // Intersection Observer with enhanced animations
  // const [isLookbookVisible, setIsLookbookVisible] = useState(false); // COMMENTED OUT - Lookbook section disabled
  const [isFeaturedVisible, setIsFeaturedVisible] = useState(false);

  useEffect(() => {
    const observers = [
      // {
      //   ref: lookbookRef,
      //   setter: setIsLookbookVisible,
      // }, // COMMENTED OUT - Lookbook section disabled
      {
        ref: featuredRef,
        setter: setIsFeaturedVisible,
      },
    ];

    const observerInstances = observers.map(({ ref, setter }) => {
      const observer = new IntersectionObserver(
        ([entry]) => setter(entry.isIntersecting),
        { threshold: 0.1, rootMargin: '100px' }
      );
      if (ref.current) observer.observe(ref.current);
      return { observer, ref };
    });

    return () => {
      observerInstances.forEach(({ observer, ref }) => {
        if (ref.current) observer.unobserve(ref.current);
      });
    };
  }, []);

  // Enhanced mouse tracking
  const handleMouseMove = useCallback((e) => {
    const { clientX, clientY } = e;
    setMousePosition({ x: clientX, y: clientY });
    mouseX.set(clientX);
    mouseY.set(clientY);
  }, [mouseX, mouseY]);

  useEffect(() => {
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [handleMouseMove]);

  // Load featured products from API
  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        setLoading(true);
        const products = await dataService.getBestsellerProducts(8);
        setFeaturedProducts(products);
      } catch (error) {
        // Fallback to empty array - component will handle gracefully
        setFeaturedProducts([]);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedProducts();
  }, []);



  // Scroll to top when component mounts (when navigating back to homepage)
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Add scroll event listeners for indicators
  useEffect(() => {
    const bestSellerContainer = bestSellerScrollRef.current;

    if (bestSellerContainer) {
      bestSellerContainer.addEventListener('scroll', handleBestSellerScroll);
    }

    return () => {
      if (bestSellerContainer) {
        bestSellerContainer.removeEventListener('scroll', handleBestSellerScroll);
      }
    };
  }, []);

  // const lookbookImages = [ // COMMENTED OUT - Lookbook section disabled
  //   "https://images.unsplash.com/photo-1551833726-b6e7210484c2?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1499939667766-4afceb292d05?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1536243298747-ea8874136d64?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1507680434567-5739c80be1ac?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1550123297-69e7c3b3e9d1?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1516826957135-700dedea698c?q=80&w=700&auto=format&fit=crop"
  // ];

  const handleExploreClick = () => {
    window.scrollTo({
      top: window.innerHeight,
      behavior: 'smooth',
    });
  };

  // Handler for search bar click - redirect to search page
  const handleSearchClick = () => {
    navigate('/search');
  };



  return (
    <div className="bg-black overflow-x-hidden" ref={containerRef}>
      {/* SEO Head for Homepage */}
      <SEOHead {...SEOPresets.home} />

      <FloatingElements />

      {/* Enhanced Marquee - COMMENTED OUT */}
      {/* <Suspense fallback={<div className="h-8 bg-slate-800" />}>
        <motion.div
          initial={{ y: -50 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <MarqueeText
            text="🔥 FREE WORLDWIDE SHIPPING ON ALL ORDERS OVER $150 • ✨ NEW SUMMER COLLECTION DROPPING SOON • 💥 20% OFF FOR MEMBERS • 🚀 LIMITED TIME EXCLUSIVE DROPS"
            className="border-b border-slate-700 h-12 bg-gradient-to-r from-slate-900 via-blue-900/30 to-slate-900 text-white"
            speed={60}
          />
        </motion.div>
      </Suspense> */}

      {/* Auto-Scrolling Creative Hero Banners */}
      {/* <AutoScrollingHeroBanners onSearchClick={handleSearchClick} /> */}

      {/* 3D PRODUCT HERO CAROUSEL - WOLFFOXX SHOWCASE */}
      <Suspense fallback={<PremiumLoader height="py-16" text="Loading WOLFFOXX Collection" />}>
        <Product3DHeroCarousel excludeProductId="2" />
      </Suspense>

      {/* NEW LATEST DROP SECTION - RIGHT AFTER HERO */}
      <Suspense fallback={<PremiumLoader height="py-12" text="Loading latest products" />}>
        <LatestDropSection excludeProductId="2" />
      </Suspense>

            <Suspense fallback={<PremiumLoader height="py-24" text="Loading statistics" />}>
        <EnhancedStatsSection/>
      </Suspense>

      {/* NEW MORE FROM BLUORNG SECTION */}
      <Suspense fallback={<PremiumLoader height="py-12" text="Loading featured products" />}>
        <MoreFromBluorngSection excludeProductId="2" />
      </Suspense>

      {/* Enhanced Streetwear Lookbook with Vertical Video - COMMENTED OUT */}
      {/* <section ref={lookbookRef} className="py-16 md:py-24 bg-black overflow-hidden relative">

        <div className="container mx-auto px-4 md:px-8 relative z-10">
          <motion.div
            className="text-center mb-12 md:mb-16"
            initial={{ opacity: 0, y: 50 }}
            animate={isLookbookVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <motion.h2
              className="text-4xl md:text-5xl lg:text-7xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-4 md:mb-6"
              whileHover={{ scale: 1.02 }}
            >
              STREETWEAR LOOKBOOK
            </motion.h2>
            <p className="text-gray-400 text-base md:text-lg max-w-3xl mx-auto leading-relaxed px-4">
              Styling inspiration for the urban explorer. Mix and match our oversized pieces
              for the ultimate street aesthetic that defines your unique identity.
            </p>
          </motion.div>

          {/* Mobile-First Grid Layout with Vertical Video */}
          {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
            {/* Featured Vertical Video - Takes full height on mobile */}
            {/* <motion.div
              className="relative group cursor-pointer lg:row-span-2 h-[500px] md:h-[600px] lg:h-[700px]"
              initial={{ opacity: 0, y: 50, rotateX: 10 }}
              animate={isLookbookVisible ? {
                opacity: 1,
                y: 0,
                rotateX: 0
              } : {}}
              transition={{
                duration: 0.8,
                delay: 0.1,
                type: "spring",
                stiffness: 100
              }}
              whileHover={{
                y: -5,
                scale: 1.02
              }}
            >
              <div className="relative w-full h-full rounded-2xl overflow-hidden bg-[#0a0a0a] shadow-2xl">
                <video
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                  autoPlay
                  muted
                  loop
                  playsInline
                  poster="https://images.unsplash.com/photo-1551833726-b6e7210484c2?q=80&w=400&auto=format&fit=crop"
                >
                  <source src="/videos/streetwear-lookbook-vertical.mp4" type="video/mp4" />
                  {/* Fallback image if video doesn't load */}
                  {/* <img
                    src="https://images.unsplash.com/photo-1551833726-b6e7210484c2?q=80&w=400&auto=format&fit=crop"
                    alt="Streetwear Lookbook"
                    className="w-full h-full object-cover"
                  />
                </video>

                {/* Video Overlay */}
                {/* <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Play Icon Indicator */}
                {/* <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                    <div className="w-0 h-0 border-l-[6px] border-l-white border-y-[4px] border-y-transparent ml-1"></div>
                  </div>
                </div>

                {/* Content Overlay */}
                {/* <div className="absolute bottom-0 left-0 right-0 p-4 md:p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500">
                  <h3 className="text-white font-bold text-lg md:text-xl mb-2">
                    STREET STYLE ESSENTIALS
                  </h3>
                  <p className="text-gray-300 text-sm">
                    Watch how our oversized pieces create the perfect street aesthetic
                  </p>
                </div>
              </div>
            </motion.div> */}

            {/* Image Grid Items */}
            {/* {lookbookImages.slice(0, 5).map((image, index) => (
              <motion.div
                key={index}
                className={`relative group cursor-pointer h-[240px] md:h-[280px] ${
                  index === 2 ? 'md:col-span-2' : ''
                }`}
                initial={{ opacity: 0, y: 50, rotateX: 10 }}
                animate={isLookbookVisible ? {
                  opacity: 1,
                  y: 0,
                  rotateX: 0
                } : {}}
                transition={{
                  duration: 0.8,
                  delay: (index + 1) * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  y: -5,
                  scale: 1.02
                }}
              >
                <div className="relative w-full h-full rounded-2xl overflow-hidden bg-[#0a0a0a] shadow-2xl">
                  <img
                    src={image}
                    alt={`Lookbook ${index + 1}`}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    loading="lazy"
                  />

                  {/* Gradient Overlay */}
                  {/* <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Floating Elements */}
                  {/* <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className="absolute top-3 left-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                    </div>
                    <div className="absolute top-3 right-3">
                      <div className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.5s'}} />
                    </div>
                  </div>

                  {/* Content Overlay */}
                  {/* <div className="absolute bottom-0 left-0 right-0 p-3 md:p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500">
                    <h3 className="text-white font-bold text-sm md:text-base mb-1">
                      {['OVERSIZED VIBES', 'STREET READY', 'URBAN COMFORT', 'LAYERED LOOKS', 'BOLD STATEMENTS'][index]}
                    </h3>
                    <p className="text-gray-300 text-xs md:text-sm">
                      {['Perfect oversized fits', 'Street-ready combinations', 'Comfort meets style', 'Master the art of layering', 'Make a statement'][index]}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))} */}
          {/* </div>

          <motion.div
            className="text-center mt-12 md:mt-16"
            initial={{ opacity: 0 }}
            animate={isLookbookVisible ? { opacity: 1 } : {}}
            transition={{ delay: 1 }}
          >
            <Link
              to="/lookbook"
              className="group inline-flex items-center gap-3 text-white text-base md:text-lg border-b-2 border-blue-500 pb-2 font-medium hover:text-blue-400 transition-all duration-300"
            >
              EXPLORE FULL LOOKBOOK
              <ArrowRight size={20} className="group-hover:translate-x-2 transition-transform" />
            </Link>
          </motion.div>
        </div>
      </section> */}



      {/* Enhanced Why Choose Us */}
      <Suspense fallback={<PremiumLoader height="py-12" />}>
        <WhyChooseUsOptimized />
      </Suspense>

      {/* Enhanced Testimonials */}
      <Suspense fallback={<PremiumLoader height="py-16" text="Loading testimonials" />}>
        <TestimonialsSectionOptimized />
      </Suspense>

      {/* Enhanced Subscription */}
      <Suspense fallback={<PremiumLoader height="py-12" />}>
        <SubscriptionSectionOptimized />
      </Suspense>

      {/* Enhanced Instagram Feed */}
      <Suspense fallback={<PremiumLoader height="py-12" text="Loading Instagram feed" />}>
        <InstagramFeedOptimized />
      </Suspense>

      {/* NEW FAQ Section */}
      <Suspense fallback={<PremiumLoader height="py-16" text="Loading FAQ" />}>
        <FAQSection />
      </Suspense>
    </div>
  );
}

const LatestDropSection = ({ excludeProductId = null }) => {
  const [latestProducts, setLatestProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedColors, setSelectedColors] = useState({}); // Track selected color for each product
  const [currentImages, setCurrentImages] = useState({}); // Track current image for each product
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  useEffect(() => {
    const loadLatestProducts = async () => {
      try {
        setLoading(true);
        const allProducts = await dataService.getNewProducts(16); // Get more products to account for T-shirt filtering

        // Filter for only T-Shirts
        let filteredProducts = allProducts.filter(product =>
          product.category === 'T-Shirts'
        );

        // Filter out excluded product
        if (excludeProductId) {
          filteredProducts = filteredProducts.filter(product => product.id !== parseInt(excludeProductId));
        }

        // If we don't have enough new T-shirts, get all T-shirts
        if (filteredProducts.length < 4) {
          const allTShirts = await dataService.getProductsByCategory('T-Shirts', {}, 1, 12);
          let additionalTShirts = allTShirts.products || [];

          // Filter out excluded product and already included products
          if (excludeProductId) {
            additionalTShirts = additionalTShirts.filter(product => product.id !== parseInt(excludeProductId));
          }
          additionalTShirts = additionalTShirts.filter(product =>
            !filteredProducts.find(existing => existing.id === product.id)
          );

          // Combine new T-shirts with additional T-shirts
          filteredProducts = [...filteredProducts, ...additionalTShirts];
        }

        // Take only 4 products after filtering
        const products = filteredProducts.slice(0, 4);
        setLatestProducts(products);

        // Initialize selected colors and current images
        const initialColors = {};
        const initialImages = {};
        products.forEach(product => {
          initialColors[product.id] = 0; // Default to first color
          initialImages[product.id] = 0; // Default to first image
        });
        setSelectedColors(initialColors);
        setCurrentImages(initialImages);

        // SUPER AGGRESSIVE preloading - ALL images and color variants for instant switching
        const preloadPromises = [];
        products.forEach(product => {
          // Preload ALL main product images (all variants)
          if (product.images && product.images.length > 0) {
            product.images.forEach(imageUrl => {
              const optimizedUrl = optimizeImageUrl(imageUrl, 800, 1000, 'good');
              preloadPromises.push(new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve();
                img.onerror = () => resolve();
                img.src = optimizedUrl;
              }));
            });
          }

          // Preload ALL color variant images for instant color switching
          if (product.colors && product.colors.length > 0) {
            product.colors.forEach(color => {
              if (color.images && color.images.length > 0) {
                color.images.forEach(imageUrl => {
                  const optimizedUrl = optimizeImageUrl(imageUrl, 800, 1000, 'good');
                  preloadPromises.push(new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => resolve();
                    img.onerror = () => resolve();
                    img.src = optimizedUrl;
                  }));
                });
              }
            });
          }

          // Fallback: preload main image if no specific images
          if (product.image_url && (!product.images || product.images.length === 0)) {
            const optimizedUrl = optimizeImageUrl(product.image_url, 800, 1000, 'good');
            preloadPromises.push(new Promise((resolve) => {
              const img = new Image();
              img.onload = () => resolve();
              img.onerror = () => resolve();
              img.src = optimizedUrl;
            }));
          }
        });

        // Wait for all images to preload before showing content
        Promise.all(preloadPromises).then(() => {
          setImagesPreloaded(true);
          setLoading(false);
        });

      } catch (error) {
        setLatestProducts([]);
        setLoading(false);
      }
    };

    loadLatestProducts();
  }, [excludeProductId]);

  // Get current images based on selected color
  const getCurrentImages = (product) => {
    const selectedColorIndex = selectedColors[product.id] || 0;
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  // Handle color selection with instant switching
  const handleColorSelect = (productId, colorIndex) => {
    setSelectedColors(prev => ({
      ...prev,
      [productId]: colorIndex
    }));
    setCurrentImages(prev => ({
      ...prev,
      [productId]: 0 // Reset to first image when color changes
    }));
  };

  // Preload color images on hover for instant switching
  const handleColorHover = (product, colorIndex) => {
    if (product.colors && product.colors[colorIndex] && product.colors[colorIndex].images) {
      product.colors[colorIndex].images.forEach(imageUrl => {
        const optimizedUrl = optimizeImageUrl(imageUrl, 800, 1000, 'good');
        // Create image element to trigger browser caching
        const img = new Image();
        img.src = optimizedUrl;
      });
    }
  };

  // Handle image navigation
  const handleImageNavigation = (productId, direction, totalImages) => {
    setCurrentImages(prev => {
      const currentIndex = prev[productId] || 0;
      let newIndex;

      if (direction === 'next') {
        newIndex = currentIndex >= totalImages - 1 ? 0 : currentIndex + 1;
      } else {
        newIndex = currentIndex <= 0 ? totalImages - 1 : currentIndex - 1;
      }

      return {
        ...prev,
        [productId]: newIndex
      };
    });
  };

  // Touch handlers for mobile swipe
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = (productId, totalImages) => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      handleImageNavigation(productId, 'next', totalImages);
    }
    if (isRightSwipe) {
      handleImageNavigation(productId, 'prev', totalImages);
    }
  };

  if (loading) {
    return <PremiumLoader height="py-16" text="Loading latest products" />;
  }

  return (
    <section className="py-8 bg-black">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl md:text-4xl font-bold text-white tracking-wider">
            LATEST DROP
          </h2>
        </div>

        {/* Products Grid - Mobile: 2 per row, Desktop: 4 per row */}
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8 mb-8">
          {latestProducts.slice(0, 4).map((product) => {
            const selectedColorIndex = selectedColors[product.id] || 0;
            const productImages = getCurrentImages(product);
            const currentImageIndex = currentImages[product.id] || 0;
            const displayImage = productImages[currentImageIndex] || product.images?.[0] || product.image_url;

            return (
              <Link
                key={product.id}
                to={`/product/${product.id}`}
                className="group cursor-pointer"
              >
                <div className="relative bg-[#0a0a0a] rounded-2xl overflow-hidden border border-[#2a2a2a] transition-all duration-300 group-hover:border-[#404040] h-96 md:h-[500px] lg:h-[600px]">
                  {/* Product Image - MUCH LARGER */}
                  <div
                    className="relative h-80 md:h-[420px] lg:h-[520px] overflow-hidden"
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={() => handleTouchEnd(product.id, productImages.length)}
                  >
                    <img
                      src={optimizeImageUrl(displayImage, 800, 1000, 'good')}
                      alt={product.name}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                      loading="eager"
                      fetchPriority="high"
                      onError={(e) => {
                        e.target.src = 'https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=80&w=800&auto=format&fit=crop&ixlib=rb-4.0.3';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/5 group-hover:bg-black/10 transition-all duration-300" />

                    {/* Desktop Navigation Arrows - Always Visible */}
                    {productImages.length > 1 && (
                      <>
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleImageNavigation(product.id, 'prev', productImages.length);
                          }}
                          className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-300 hover:bg-[#FF6B35] hover:scale-110 hidden md:flex"
                        >
                          <ChevronLeft size={16} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleImageNavigation(product.id, 'next', productImages.length);
                          }}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-300 hover:bg-[#FF6B35] hover:scale-110 hidden md:flex"
                        >
                          <ChevronRight size={16} />
                        </button>
                      </>
                    )}

                    {/* Wishlist Button - Always Visible */}
                    <div className="absolute top-3 right-3 z-10">
                      <WishlistButton
                        productId={product.id}
                        productName={product.name}
                        productPrice={product.sale_price || product.price}
                        productImage={displayImage}
                        className="bg-[#2a2a2a] hover:bg-[#404040] text-white w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200"
                      />
                    </div>

                    {/* Sale Badge */}
                    {product.sale_price && (
                      <div className="absolute top-4 left-4">
                        <span className="bg-red-600 text-white text-sm font-bold px-3 py-2 rounded-lg shadow-lg">
                          SALE
                        </span>
                      </div>
                    )}


                  </div>

                  {/* Product Info */}
                  <div className="p-3 md:p-4">
                    {/* Small Title */}
                    <h3 className="text-white font-medium text-xs md:text-sm mb-1 line-clamp-1 group-hover:text-[#FF6B35] transition-colors duration-300">
                      {product.name}
                    </h3>

                    {/* Price and Color Selection in Same Row */}
                    <div className="flex items-center justify-between">
                      {/* Small Price */}
                      <div className="flex items-center gap-2">
                        {product.sale_price ? (
                          <>
                            <span className="text-[#FF6B35] font-semibold text-xs">
                              Rs. {product.sale_price}
                            </span>
                            <span className="text-gray-400 line-through text-xs">
                              Rs. {product.price}
                            </span>
                          </>
                        ) : (
                          <span className="text-white font-semibold text-xs">
                            Rs. {product.price}
                          </span>
                        )}
                      </div>

                      {/* Color Selection - Extreme Right */}
                      {product.colors && product.colors.length > 0 && (
                        <div className="flex items-center gap-1">
                          {product.colors.slice(0, 4).map((color, colorIndex) => (
                            <button
                              key={colorIndex}
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleColorSelect(product.id, colorIndex);
                              }}
                              onMouseEnter={() => handleColorHover(product, colorIndex)}
                              className={`w-3 h-3 rounded-full border transition-all duration-200 ${
                                selectedColorIndex === colorIndex
                                  ? 'border-white scale-110 shadow-lg shadow-white/25'
                                  : 'border-[#404040] hover:border-[#6a6a6a]'
                              }`}
                              style={{ backgroundColor: color.value }}
                              title={color.name}
                            />
                          ))}
                          {product.colors.length > 4 && (
                            <div className="w-3 h-3 rounded-full bg-[#2a2a2a] flex items-center justify-center text-xs text-[#9a9a9a] font-medium">
                              +{product.colors.length - 4}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Discover More Button */}
        <div className="text-center">
          <Link
            to="/collections"
            className="inline-flex items-center gap-2 text-white text-sm font-medium hover:text-[#FF6B35] transition-colors duration-300 border border-white/20 hover:border-[#FF6B35]/50 px-6 py-3 rounded-lg"
          >
            DISCOVER MORE
            <ArrowRight size={16} />
          </Link>
        </div>
      </div>
    </section>
  );
};

const MoreFromBluorngSection = ({ excludeProductId = null }) => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedColors, setSelectedColors] = useState({}); // Track selected color for each product
  const [currentImages, setCurrentImages] = useState({}); // Track current image for each product
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        setLoading(true);
        const allProducts = await dataService.getBestsellerProducts(16); // Get more products to account for filtering

        // Filter for only T-Shirts and Oversized Tees
        let filteredProducts = allProducts.filter(product =>
          product.category === 'T-Shirts' || product.category === 'Oversized Tees'
        );

        // Filter out excluded product
        if (excludeProductId) {
          filteredProducts = filteredProducts.filter(product => product.id !== parseInt(excludeProductId));
        }

        // Take only 8 products after filtering
        const products = filteredProducts.slice(0, 8);
        setFeaturedProducts(products);

        // Initialize selected colors and current images
        const initialColors = {};
        const initialImages = {};
        products.forEach(product => {
          initialColors[product.id] = 0; // Default to first color
          initialImages[product.id] = 0; // Default to first image
        });
        setSelectedColors(initialColors);
        setCurrentImages(initialImages);

        // SMART preloading - Only critical above-the-fold images
        const preloadPromises = [];
        products.forEach((product, index) => {
          // For first 2 products (above fold) - preload ONLY first image for instant LCP
          if (index < 2) {
            // Preload ONLY the first main product image (hero image)
            if (product.images && product.images.length > 0) {
              const firstImageUrl = product.images[0];
              const optimizedUrl = optimizeImageUrl(firstImageUrl, 800, 1000, 'good');
              preloadPromises.push(new Promise((resolve) => {
                const img = new Image();
                img.fetchPriority = 'high';
                img.loading = 'eager';
                img.decoding = 'async';
                img.onload = () => resolve();
                img.onerror = () => resolve();
                img.src = optimizedUrl;
              }));
            }

            // Preload ALL color variant images for instant color switching
            if (product.colors && product.colors.length > 0) {
              product.colors.forEach(color => {
                if (color.images && color.images.length > 0) {
                  color.images.forEach(imageUrl => {
                    const optimizedUrl = optimizeImageUrl(imageUrl, 800, 1000, 'good');
                    preloadPromises.push(new Promise((resolve) => {
                      const img = new Image();
                      img.onload = () => resolve();
                      img.onerror = () => resolve();
                      img.src = optimizedUrl;
                    }));
                  });
                }
              });
            }
          } else {
            // For remaining products - preload only main image and first color variant
            const mainImageUrl = optimizeImageUrl(product.images?.[0] || product.image_url, 800, 1000, 'good');
            preloadPromises.push(new Promise((resolve) => {
              const img = new Image();
              img.onload = () => resolve();
              img.onerror = () => resolve();
              img.src = mainImageUrl;
            }));

            // Preload first color variant for quick switching
            if (product.colors && product.colors[0] && product.colors[0].images) {
              product.colors[0].images.slice(0, 2).forEach(imageUrl => {
                const optimizedUrl = optimizeImageUrl(imageUrl, 800, 1000, 'good');
                preloadPromises.push(new Promise((resolve) => {
                  const img = new Image();
                  img.onload = () => resolve();
                  img.onerror = () => resolve();
                  img.src = optimizedUrl;
                }));
              });
            }
          }

          // Fallback: preload main image if no specific images
          if (product.image_url && (!product.images || product.images.length === 0)) {
            const optimizedUrl = optimizeImageUrl(product.image_url, 800, 1000, 'good');
            preloadPromises.push(new Promise((resolve) => {
              const img = new Image();
              img.onload = () => resolve();
              img.onerror = () => resolve();
              img.src = optimizedUrl;
            }));
          }
        });

        // Wait for critical images to preload before showing content
        Promise.all(preloadPromises).then(() => {
          setImagesPreloaded(true);
          setLoading(false);
        });
      } catch (error) {
        setFeaturedProducts([]);
        setLoading(false);
      }
    };

    loadFeaturedProducts();
  }, [excludeProductId]);

  // Get current images based on selected color
  const getCurrentImages = (product) => {
    const selectedColorIndex = selectedColors[product.id] || 0;
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  // Handle color selection with instant switching
  const handleColorSelect = (productId, colorIndex) => {
    setSelectedColors(prev => ({
      ...prev,
      [productId]: colorIndex
    }));
    setCurrentImages(prev => ({
      ...prev,
      [productId]: 0 // Reset to first image when color changes
    }));
  };

  // Preload color images on hover for instant switching
  const handleColorHover = (product, colorIndex) => {
    if (product.colors && product.colors[colorIndex] && product.colors[colorIndex].images) {
      product.colors[colorIndex].images.forEach(imageUrl => {
        const optimizedUrl = optimizeImageUrl(imageUrl, 800, 1000, 'good');
        // Create image element to trigger browser caching
        const img = new Image();
        img.src = optimizedUrl;
      });
    }
  };

  // Handle image navigation
  const handleImageNavigation = (productId, direction, totalImages) => {
    setCurrentImages(prev => {
      const currentIndex = prev[productId] || 0;
      let newIndex;

      if (direction === 'next') {
        newIndex = currentIndex >= totalImages - 1 ? 0 : currentIndex + 1;
      } else {
        newIndex = currentIndex <= 0 ? totalImages - 1 : currentIndex - 1;
      }

      return {
        ...prev,
        [productId]: newIndex
      };
    });
  };

  // Touch handlers for mobile swipe
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = (productId, totalImages) => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      handleImageNavigation(productId, 'next', totalImages);
    }
    if (isRightSwipe) {
      handleImageNavigation(productId, 'prev', totalImages);
    }
  };

  if (loading) {
    return <PremiumLoader height="py-16" text="Loading featured products" />;
  }

  return (
    <section className="py-8 bg-black">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl md:text-4xl font-bold text-white tracking-wider">
            MORE FROM WOLFFOXX
          </h2>
        </div>

        {/* Products Grid - Mobile: 2 per row, Desktop: 4 per row */}
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
          {featuredProducts.slice(0, 8).map((product) => {
            const selectedColorIndex = selectedColors[product.id] || 0;
            const productImages = getCurrentImages(product);
            const currentImageIndex = currentImages[product.id] || 0;
            const displayImage = productImages[currentImageIndex] || product.images?.[0] || product.image_url;

            return (
              <Link
                key={product.id}
                to={`/product/${product.id}`}
                className="group cursor-pointer"
              >
                <div className="relative bg-[#0a0a0a] rounded-2xl overflow-hidden border border-[#2a2a2a] transition-all duration-300 group-hover:border-[#404040] h-96 md:h-[480px] lg:h-[520px]">
                  {/* Product Image - MUCH LARGER */}
                  <div
                    className="relative h-80 md:h-[400px] lg:h-[440px] overflow-hidden"
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={() => handleTouchEnd(product.id, productImages.length)}
                  >
                    <img
                      src={optimizeImageUrl(displayImage, 800, 1000, 'good')}
                      alt={product.name}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                      loading="eager"
                      fetchPriority="high"
                      onError={(e) => {
                        e.target.src = 'https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=80&w=800&auto=format&fit=crop&ixlib=rb-4.0.3';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/5 group-hover:bg-black/10 transition-all duration-300" />

                    {/* Desktop Navigation Arrows - Always Visible */}
                    {productImages.length > 1 && (
                      <>
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleImageNavigation(product.id, 'prev', productImages.length);
                          }}
                          className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-300 hover:bg-[#FF6B35] hover:scale-110 hidden md:flex"
                        >
                          <ChevronLeft size={16} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleImageNavigation(product.id, 'next', productImages.length);
                          }}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-300 hover:bg-[#FF6B35] hover:scale-110 hidden md:flex"
                        >
                          <ChevronRight size={16} />
                        </button>
                      </>
                    )}

                    {/* Wishlist Button - Always Visible */}
                    <div className="absolute top-3 right-3 z-10">
                      <WishlistButton
                        productId={product.id}
                        productName={product.name}
                        productPrice={product.sale_price || product.price}
                        productImage={displayImage}
                        className="bg-[#2a2a2a] hover:bg-[#404040] text-white w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200"
                      />
                    </div>

                    {/* Sale Badge */}
                    {product.sale_price && (
                      <div className="absolute top-4 left-4">
                        <span className="bg-red-600 text-white text-sm font-bold px-3 py-2 rounded-lg shadow-lg">
                          SALE
                        </span>
                      </div>
                    )}


                  </div>



                  {/* Product Info */}
                  <div className="p-3 md:p-4">
                    {/* Small Title */}
                    <h3 className="text-white font-medium text-xs md:text-sm mb-1 line-clamp-1 group-hover:text-[#FF6B35] transition-colors duration-300">
                      {product.name}
                    </h3>

                    {/* Price and Color Selection in Same Row */}
                    <div className="flex items-center justify-between">
                      {/* Small Price */}
                      <div className="flex items-center gap-2">
                        {product.sale_price ? (
                          <>
                            <span className="text-[#FF6B35] font-semibold text-xs">
                              Rs. {product.sale_price}
                            </span>
                            <span className="text-gray-400 line-through text-xs">
                              Rs. {product.price}
                            </span>
                          </>
                        ) : (
                          <span className="text-white font-semibold text-xs">
                            Rs. {product.price}
                          </span>
                        )}
                      </div>

                      {/* Color Selection - Extreme Right */}
                      {product.colors && product.colors.length > 0 && (
                        <div className="flex items-center gap-1">
                          {product.colors.slice(0, 4).map((color, colorIndex) => (
                            <button
                              key={colorIndex}
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleColorSelect(product.id, colorIndex);
                              }}
                              onMouseEnter={() => handleColorHover(product, colorIndex)}
                              className={`w-3 h-3 rounded-full border transition-all duration-200 ${
                                selectedColorIndex === colorIndex
                                  ? 'border-white scale-110 shadow-lg shadow-white/25'
                                  : 'border-[#404040] hover:border-[#6a6a6a]'
                              }`}
                              style={{ backgroundColor: color.value }}
                              title={color.name}
                            />
                          ))}
                          {product.colors.length > 4 && (
                            <div className="w-3 h-3 rounded-full bg-[#2a2a2a] flex items-center justify-center text-xs text-[#9a9a9a] font-medium">
                              +{product.colors.length - 4}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Discover More Button - Below Grid */}
        <div className="text-center mt-8">
          <Link
            to="/collections"
            className="inline-flex items-center gap-2 text-white text-sm font-medium hover:text-[#FF6B35] transition-colors duration-300 border border-white/20 hover:border-[#FF6B35]/50 px-6 py-3 rounded-lg"
          >
            DISCOVER MORE
            <ArrowRight size={16} />
          </Link>
        </div>
      </div>
    </section>
  );
};

const CategoryTagsOptimized = () => {
  const [categoryScrollIndex, setCategoryScrollIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const categoryScrollRef = useRef(null);

  const handleCategoryScroll = () => {
    if (categoryScrollRef.current) {
      const container = categoryScrollRef.current;
      const scrollLeft = container.scrollLeft;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16; // gap-4
      const totalItemWidth = itemWidth + gap;
      const index = Math.round(scrollLeft / totalItemWidth);
      setCategoryScrollIndex(Math.min(index, 3)); // 4 categories
    }
  };

  const scrollToCategory = (index) => {
    if (categoryScrollRef.current) {
      const container = categoryScrollRef.current;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16;
      const totalItemWidth = itemWidth + gap;
      const scrollLeft = index * totalItemWidth;
      container.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    const categoryContainer = categoryScrollRef.current;
    if (categoryContainer) {
      categoryContainer.addEventListener('scroll', handleCategoryScroll);
    }
    return () => {
      if (categoryContainer) {
        categoryContainer.removeEventListener('scroll', handleCategoryScroll);
      }
    };
  }, []);

  const categories = [
    {
      name: "OVERSIZED TEES",
      color: "from-violet-600 via-purple-600 to-indigo-600",
      hoverColor: "from-violet-400 via-purple-400 to-indigo-400",
      shadow: "violet-500/40",
      glowColor: "rgba(139, 92, 246, 0.6)",
      icon: Crown,
      route: "/category/oversized-tees",
      image: "https://images.unsplash.com/photo-1576566588028-4147f3842f27?q=80&w=700&auto=format&fit=crop"
    },
    {
      name: "T-SHIRTS",
      color: "from-cyan-600 via-blue-600 to-indigo-600",
      hoverColor: "from-cyan-400 via-blue-400 to-indigo-400",
      shadow: "cyan-500/40",
      glowColor: "rgba(6, 182, 212, 0.6)",
      icon: Star,
      route: "/category/t-shirts",
      image: "https://images.unsplash.com/photo-1618354691438-25bc04584c23?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3",

    },
    {
      name: "HOODIES",
      color: "from-emerald-600 via-teal-600 to-cyan-600",
      hoverColor: "from-emerald-400 via-teal-400 to-cyan-400",
      shadow: "emerald-500/40",
      glowColor: "rgba(16, 185, 129, 0.6)",
      icon: Zap,
      route: "/category/hoodies",
      image: "https://images.unsplash.com/photo-1556821840-3a63f95609a7?q=80&w=700&auto=format&fit=crop"
    },
    {
      name: "SHIRTS",
      color: "from-rose-600 via-pink-600 to-purple-600",
      hoverColor: "from-rose-400 via-pink-400 to-purple-400",
      shadow: "rose-500/40",
      glowColor: "rgba(244, 63, 94, 0.6)",
      icon: Award,
      route: "/category/shirts",
      image: "https://images.unsplash.com/photo-1607345366928-199ea26cfe3e?q=80&w=700&auto=format&fit=crop"
    }
  ];

  return (
    <motion.section
      className="pt-8 pb-20 bg-black relative overflow-hidden"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8 }}
      onViewportEnter={() => setIsVisible(true)}
    >


      {/* Enhanced Particles */}
      <motion.div
        className="absolute inset-0 overflow-hidden"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1, delay: 0.3 }}
      >
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-blue-400/15 via-purple-400/15 to-pink-400/15 animate-pulse"
            initial={{ scale: 0, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: i * 0.05 }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}
      </motion.div>

      {/* Floating geometric shapes */}
      <motion.div
        className="absolute inset-0 overflow-hidden pointer-events-none"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1, delay: 0.5 }}
      >
        <motion.div
          className="absolute top-1/4 left-1/4 w-32 h-32 border border-blue-500/10 rounded-full animate-spin"
          style={{animationDuration: '20s'}}
          initial={{ scale: 0, rotate: 0 }}
          whileInView={{ scale: 1, rotate: 360 }}
          viewport={{ once: true }}
          transition={{ duration: 2, delay: 0.6 }}
        />
        <motion.div
          className="absolute top-3/4 right-1/4 w-24 h-24 border border-purple-500/10 rounded-lg rotate-45 animate-pulse"
          initial={{ scale: 0, rotate: 0 }}
          whileInView={{ scale: 1, rotate: 45 }}
          viewport={{ once: true }}
          transition={{ duration: 1.5, delay: 0.8 }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/3 w-16 h-16 border border-pink-500/10 rounded-full animate-bounce"
          style={{animationDuration: '3s'}}
          initial={{ scale: 0, y: 20 }}
          whileInView={{ scale: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 1 }}
        />
      </motion.div>

      <div className="container mx-auto px-6 md:px-12 relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 tracking-tight"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            SHOP BY <motion.span
              className="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >CATEGORY</motion.span>
          </motion.h2>

          <motion.div
            className="relative max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed font-light">
              Discover your perfect style across our curated collections designed for the modern trendsetter
            </p>
          </motion.div>
        </motion.div>

        {/* Mobile: Horizontal Scroller */}
        <motion.div
          className="block sm:hidden"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="relative">
            <div
              ref={categoryScrollRef}
              className="flex gap-4 overflow-x-auto scrollbar-hide pb-4 px-1 scroll-smooth"
            >
              {categories.map((category, index) => (
                <div
                  key={index}
                  className="flex-none w-80"
                >
                  <Link
                    to={category.route}
                    className="group cursor-pointer relative block"
                  >
                    {/* Full Image Container */}
                    <div className="relative rounded-2xl overflow-hidden h-90 border border-[#2a2a2a] transition-all duration-300 group-hover:border-[#404040]">
                      {/* Background Image */}
                      <img
                        src={category.image}
                        alt={category.name}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent" />

                      {/* Content Overlay */}
                      <div className="absolute inset-0 flex flex-col justify-end p-6">
                        <div className="space-y-3">
                          <h3 className="text-xl font-bold text-white tracking-wide">
                            {category.name}
                          </h3>
                          <p className="text-sm text-gray-200 opacity-90">
                            Premium Collection
                          </p>
                          <div className="flex items-center justify-between pt-2">
                            <div className="flex items-center text-white/90 text-sm font-medium">
                              <span>Shop Now</span>
                              <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                            </div>
                            <div className="w-8 h-0.5 bg-white/60 rounded-full" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            {/* Gradient overlays for scroll indication */}
            {/* <div className="absolute left-0 top-0 bottom-4 w-8 bg-gradient-to-r from-gray-900 to-transparent pointer-events-none z-10" />
            <div className="absolute right-0 top-0 bottom-4 w-8 bg-gradient-to-l from-gray-900 to-transparent pointer-events-none z-10" /> */}


          </div>
        </motion.div>

        {/* Tablet and Desktop: Horizontal Scroller with Arrows */}
        <motion.div
          className="hidden sm:block"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="relative">
            <div
              ref={categoryScrollRef}
              className="flex gap-6 lg:gap-8 overflow-x-auto scrollbar-hide pb-4 px-1 scroll-smooth"
            >
              {categories.map((category, index) => (
                <motion.div
                  key={index}
                  className="flex-none w-80 lg:w-96"
                  initial={{ opacity: 0, scale: 0.9, y: 20 }}
                  whileInView={{ opacity: 1, scale: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.6 + index * 0.05 }}
                >
                  <Link
                    to={category.route}
                    className="group cursor-pointer relative block"
                  >
                    {/* Main Card Container */}
                    <div className="relative bg-[#0a0a0a] backdrop-blur-sm rounded-2xl overflow-hidden border border-[#2a2a2a] transition-all duration-300 group-hover:border-[#404040] h-80">
                      {/* Image Container */}
                      <div className="relative h-56 overflow-hidden">
                        <img
                          src={category.image}
                          alt={category.name}
                          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-black/20" />
                      </div>

                      {/* Content */}
                      <div className="p-6">
                        <h3 className="text-lg font-semibold text-white mb-2 tracking-wide">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-400 mb-4">
                          Premium collection
                        </p>
                        <div className="flex items-center text-gray-300 text-sm">
                          <span>Explore</span>
                          <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>

            {/* Desktop Navigation Arrows */}
            <button
              onClick={() => {
                if (categoryScrollRef.current) {
                  categoryScrollRef.current.scrollBy({ left: -400, behavior: 'smooth' });
                }
              }}
              className="absolute left-2 top-1/2 -translate-y-1/2 w-12 h-12 bg-[#2a2a2a] hover:bg-[#404040] text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100 shadow-lg"
            >
              <ChevronLeft size={20} />
            </button>
            <button
              onClick={() => {
                if (categoryScrollRef.current) {
                  categoryScrollRef.current.scrollBy({ left: 400, behavior: 'smooth' });
                }
              }}
              className="absolute right-2 top-1/2 -translate-y-1/2 w-12 h-12 bg-[#2a2a2a] hover:bg-[#404040] text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100 shadow-lg"
            >
              <ChevronRight size={20} />
            </button>

            {/* Gradient overlays for scroll indication */}
            <div className="absolute left-0 top-0 bottom-4 w-8 bg-gradient-to-r from-black to-transparent pointer-events-none z-10" />
            <div className="absolute right-0 top-0 bottom-4 w-8 bg-gradient-to-l from-black to-transparent pointer-events-none z-10" />
          </div>
        </motion.div>

        {/* Enhanced Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <motion.div
            initial={{ scale: 0.95 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.9 }}
          >
            <Link
              to="/collections"
              className="inline-flex items-center space-x-4 text-gray-400 text-lg font-medium bg-[#1a1a1a] backdrop-blur-lg rounded-full px-8 py-4 shadow-2xl border border-[#2a2a2a] hover:border-[#404040] transition-all duration-500 cursor-pointer group relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              <div className="flex space-x-2 relative z-10">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse" style={{animationDelay: '0.6s'}}></div>
              </div>
              <span className="group-hover:text-white transition-colors duration-300 relative z-10 text-sm">Browse all collections</span>
              <ArrowRight size={18} className="opacity-60 group-hover:opacity-100 group-hover:translate-x-2 transition-all duration-300 relative z-10" />
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
};

const WhyChooseUsOptimized = () => {
  const [isVisible, setIsVisible] = useState(false);

  const features = [
    {
      title: "PREMIUM COTTON QUALITY",
      description: "100% heavyweight cotton with 260 GSM fabric for ultimate comfort and durability that lasts wash after wash",
      icon: Zap,
      color: "from-violet-500 via-purple-500 to-indigo-500",
      bgGlow: "violet-500/20",
      glowColor: "rgba(139, 92, 246, 0.6)",
      stats: "260 GSM",
      badge: "PREMIUM"
    },
    {
      title: "FAST SHIPPING & COD",
      description: "Quick delivery across India with cash on delivery option available. Get your WOLFFOXX gear delivered to your doorstep",
      icon: Star,
      color: "from-cyan-500 via-blue-500 to-indigo-500",
      bgGlow: "cyan-500/20",
      glowColor: "rgba(6, 182, 212, 0.6)",
      stats: "Nationwide",
      badge: "POPULAR"
    },
    {
      title: "EASY EXCHANGES",
      description: "Hassle-free 15-day exchange policy if size or fit doesn't work for you. Customer satisfaction guaranteed",
      icon: Shield,
      color: "from-emerald-500 via-teal-500 to-cyan-500",
      bgGlow: "emerald-500/20",
      glowColor: "rgba(16, 185, 129, 0.6)",
      stats: "15 Days"
    },
    {
      title: "TREND-FIRST DESIGNS",
      description: "Stay ahead with our limited drops featuring exclusive graphics and colorways that define streetwear culture",
      icon: Truck,
      color: "from-blue-500 via-indigo-500 to-purple-500",
      bgGlow: "blue-500/20",
      glowColor: "rgba(59, 130, 246, 0.6)",
      stats: "Limited"
    }
  ];

  return (
    <motion.section
      className="pt-6 pb-12 sm:pt-8 sm:pb-14 md:pb-16 relative overflow-hidden bg-black"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8 }}
      onViewportEnter={() => setIsVisible(true)}
    >

      {/* Floating Elements */}
      <motion.div
        className="absolute inset-0 overflow-hidden"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1, delay: 0.3 }}
      >
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-blue-400/8 via-purple-400/10 to-pink-400/8 animate-pulse"
            initial={{ scale: 0, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: i * 0.03 }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${4 + Math.random() * 3}s`
            }}
          />
        ))}
      </motion.div>

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        {/* Header - Optimized for single line and reduced spacing */}
        <motion.div
          className="text-center mb-6 sm:mb-8"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <motion.h2
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-3 sm:mb-4 text-white whitespace-nowrap"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            WHY CHOOSE <motion.span
              className="text-blue-400"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >WOLFFOXX</motion.span>
          </motion.h2>

          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <p className="text-gray-300 text-sm md:text-base max-w-2xl mx-auto leading-relaxed font-light">
              What sets us apart from the <span className="text-blue-400 font-semibold">competition</span>.
            </p>
            {/* <motion.div
              className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-24 h-0.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full"
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.5 }}
            /> */}
          </motion.div>
        </motion.div>

        {/* Features Grid - Mobile Optimized */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 max-w-7xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <motion.div
                key={index}
                className="group relative"
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.7 + index * 0.05 }}
              >
                {/* Glow Effect */}
                <div
                  className="absolute -inset-1 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl"
                  style={{
                    background: `linear-gradient(135deg, ${feature.glowColor}, transparent, ${feature.glowColor})`
                  }}
                />

                <div className="relative bg-[#0a0a0a] backdrop-blur-xl p-4 md:p-5 rounded-2xl border border-[#2a2a2a] group-hover:border-[#404040] transition-all duration-300 h-44 md:h-48 transform group-hover:scale-105 shadow-xl group-hover:shadow-purple-500/10">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0" style={{
                      backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 80' width='80' height='80'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='1'%3E%3Cpath d='M20 20h40v40H20z'/%3E%3Cpath d='M40 20v40M20 40h40'/%3E%3C/g%3E%3C/svg%3E")`
                    }} />
                  </div>

                  {/* Stats Badge */}
                  <div className="absolute top-4 right-4 bg-[#1a1a1a] backdrop-blur-sm px-2.5 py-1 rounded-full border border-[#404040]">
                    <span className="text-xs font-bold text-gray-300">
                      {feature.stats}
                    </span>
                  </div>

                  {/* Feature Badge */}
                  {/* {feature.badge && (
                    <div className={`absolute top-4 left-4 px-2 py-1 rounded-full text-xs font-bold ${
                      feature.badge === 'NEW'
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white'
                        : 'bg-gradient-to-r from-orange-500 to-red-500 text-white'
                    } shadow-lg animate-pulse`}>
                      {feature.badge}
                    </div>
                  )} */}

                  {/* Icon */}
                  <div className={`relative w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center mb-2 md:mb-3 group-hover:shadow-xl group-hover:shadow-purple-500/20 transition-all duration-300 transform group-hover:rotate-2 group-hover:scale-110`}>
                    <IconComponent size={18} className="text-white md:w-5 md:h-5" />
                    <div className="absolute inset-0 bg-white/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Icon Glow */}
                    <div
                      className="absolute -inset-1 rounded-2xl opacity-0 group-hover:opacity-40 transition-opacity duration-300 blur-lg"
                      style={{ background: feature.glowColor }}
                    />
                  </div>

                  {/* Content */}
                  <div className="relative z-10">
                    <h3 className="text-sm md:text-base lg:text-lg font-bold text-white mb-1 md:mb-2 tracking-tight leading-tight">
                      {feature.title}
                    </h3>
                    <p className="text-gray-400 text-xs leading-relaxed group-hover:text-gray-300 transition-colors duration-300 font-light mb-1 md:mb-2">
                      {feature.description}
                    </p>

                    {/* Action Indicator */}
                    {/* <div className="flex items-center text-gray-500 group-hover:text-blue-400 transition-colors duration-300">
                      <span className="text-xs font-medium mr-2">LEARN MORE</span>
                      <ArrowRight size={12} className="group-hover:translate-x-1 transition-transform duration-300" />
                    </div> */}
                  </div>

                  {/* Corner Accents */}
                  <div className="absolute top-3 left-3 w-2 h-2 bg-white/20 rounded-full group-hover:bg-white/40 transition-colors duration-300" />
                  <div className="absolute bottom-3 right-3 w-2 h-2 bg-white/20 rounded-full group-hover:bg-white/40 transition-colors duration-300" />
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Bottom Section */}
        {/* <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.9 }}
        > */}
          {/* <motion.div
            className="inline-flex items-center space-x-3 text-gray-400 text-sm font-medium bg-[#1a1a1a] backdrop-blur-sm rounded-full px-6 py-3 shadow-xl border border-[#2a2a2a] hover:border-[#404040] transition-all duration-300 cursor-pointer group"
            initial={{ scale: 0.95 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 1 }}
          > */}
            {/* <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-1.5 h-1.5 bg-pink-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div> */}
            {/* <span className="group-hover:text-white transition-colors duration-300">
              Join the WOLFFOXX family today
            </span> */}
            {/* <Users size={14} className="opacity-60 group-hover:opacity-100 transition-opacity duration-300" />
          </motion.div>
        </motion.div> */}
      </div>
    </motion.section>
  );
};

const TestimonialsSectionOptimized = () => {
  const [testimonialScrollIndex, setTestimonialScrollIndex] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const testimonialScrollRef = useRef(null);

  const handleTestimonialScroll = () => {
    if (testimonialScrollRef.current) {
      const container = testimonialScrollRef.current;
      const scrollLeft = container.scrollLeft;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 24; // gap-6
      const totalItemWidth = itemWidth + gap;
      const index = Math.round(scrollLeft / totalItemWidth);
      setTestimonialScrollIndex(Math.min(index, 2)); // 3 testimonials
    }
  };

  const scrollToTestimonial = (index) => {
    if (testimonialScrollRef.current) {
      const container = testimonialScrollRef.current;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 24;
      const totalItemWidth = itemWidth + gap;
      const scrollLeft = index * totalItemWidth;
      container.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    }
  };

  // Enhanced touch/swipe functionality
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartX(e.pageX - testimonialScrollRef.current.offsetLeft);
    setScrollLeft(testimonialScrollRef.current.scrollLeft);
  };

  const handleTouchStart = (e) => {
    setIsDragging(true);
    setStartX(e.touches[0].pageX - testimonialScrollRef.current.offsetLeft);
    setScrollLeft(testimonialScrollRef.current.scrollLeft);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX - testimonialScrollRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    testimonialScrollRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const x = e.touches[0].pageX - testimonialScrollRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    testimonialScrollRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    const testimonialContainer = testimonialScrollRef.current;
    if (testimonialContainer) {
      testimonialContainer.addEventListener('scroll', handleTestimonialScroll);

      // Mouse events
      testimonialContainer.addEventListener('mousedown', handleMouseDown);
      testimonialContainer.addEventListener('mousemove', handleMouseMove);
      testimonialContainer.addEventListener('mouseup', handleMouseUp);
      testimonialContainer.addEventListener('mouseleave', handleMouseUp);

      // Touch events
      testimonialContainer.addEventListener('touchstart', handleTouchStart);
      testimonialContainer.addEventListener('touchmove', handleTouchMove);
      testimonialContainer.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      if (testimonialContainer) {
        testimonialContainer.removeEventListener('scroll', handleTestimonialScroll);
        testimonialContainer.removeEventListener('mousedown', handleMouseDown);
        testimonialContainer.removeEventListener('mousemove', handleMouseMove);
        testimonialContainer.removeEventListener('mouseup', handleMouseUp);
        testimonialContainer.removeEventListener('mouseleave', handleMouseUp);
        testimonialContainer.removeEventListener('touchstart', handleTouchStart);
        testimonialContainer.removeEventListener('touchmove', handleTouchMove);
        testimonialContainer.removeEventListener('touchend', handleTouchEnd);
      }
    };
  }, [isDragging, startX, scrollLeft]);

const testimonials = [
  {
    name: "Rahul Mehta",
    location: "Mumbai",
    rating: 5,
    text: "Was honestly not expecting this level of quality. The oversized fit is just perfect, and the fabric feels premium without being heavy. Totally worth it!",
    avatar: "https://randomuser.me/api/portraits/men/56.jpg" // Realistic face style, web optimized
  },
  {
    name: "Gautam Iyer",
    location: "Bangalore",
    rating: 4,
    text: "Loved the stitching and the fit! I’ve tried a lot of brands for streetwear but WOLFFOXX really nails that effortless oversized vibe. Plus, the delivery was super quick.",
    avatar: "https://randomuser.me/api/portraits/men/69.jpg"
  },
  {
    name: "Karan Singh",
    location: "Delhi",
    rating: 5,
    text: "I usually don't leave reviews but these tees deserve it. The fabric is soft, the fall is perfect, and even after washing it a few times, no shrinkage. Highly recommend!",
    avatar: "https://randomuser.me/api/portraits/men/58.jpg"
  }
];



  return (
    <motion.section
      className="py-12 bg-black relative overflow-hidden"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8 }}
      onViewportEnter={() => setIsVisible(true)}
    >

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-4"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            CUSTOMER LOVE
          </motion.h2>
          <motion.p
            className="text-gray-400 text-lg max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Don't just take our word for it. See what our customers are saying about their WOLFFOXX experience.
          </motion.p>
        </motion.div>

        {/* Mobile: Enhanced Horizontal Scroller */}
        <div className="block md:hidden">
          <div className="relative">
            <div
              ref={testimonialScrollRef}
              className="flex gap-6 overflow-x-auto scrollbar-hide pb-6 px-1 scroll-smooth cursor-grab active:cursor-grabbing"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                WebkitOverflowScrolling: 'touch'
              }}
            >
              {testimonials.map((review, index) => (
                <div key={index} className="flex-none w-80">
                  <div
                    className="group relative h-full transform-gpu transition-all duration-500 hover:scale-105"
                    style={{
                      perspective: '1000px'
                    }}
                  >
                    <div
                      className="relative bg-[#0a0a0a] backdrop-blur-xl p-8 rounded-3xl border border-[#2a2a2a] hover:border-[#404040] transition-all duration-500 h-full shadow-2xl"
                      style={{
                        transform: 'rotateX(2deg) rotateY(-2deg)',
                        boxShadow: `
                          0 25px 50px -12px rgba(0, 0, 0, 0.8),
                          0 0 0 1px rgba(255, 255, 255, 0.05),
                          inset 0 1px 0 rgba(255, 255, 255, 0.1)
                        `
                      }}
                    >
                      {/* 3D depth layers */}
                      <div
                        className="absolute inset-0 bg-gradient-to-br from-[#1a1a1a] to-transparent rounded-3xl"
                        style={{
                          transform: 'translateZ(-2px) translateX(-2px) translateY(-2px)'
                        }}
                      />

                      {/* Content */}
                      <div className="relative z-10">
                        {/* Stars */}
                        <div className="flex items-center mb-6 gap-1">
                          {[...Array(review.rating)].map((_, i) => (
                            <Star
                              key={i}
                              size={18}
                              className="text-yellow-400 fill-yellow-400 drop-shadow-sm transform transition-transform duration-300 hover:scale-110"
                            />
                          ))}
                        </div>

                        {/* Quote */}
                        <div className="relative mb-8">
                          <div className="absolute -top-2 -left-2 text-6xl text-slate-600/20 font-serif leading-none">"</div>
                          <p className="text-gray-200 text-base leading-relaxed font-light relative z-10 pl-6">
                            {review.text}
                          </p>
                          <div className="absolute -bottom-4 -right-2 text-6xl text-slate-600/20 font-serif leading-none transform rotate-180">"</div>
                        </div>

                        {/* Profile */}
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <div
                              className="w-14 h-14 rounded-full overflow-hidden border-2 border-[#404040] shadow-lg"
                              style={{
                                boxShadow: '0 8px 16px rgba(0, 0, 0, 0.4)'
                              }}
                            >
                              <img
                                src={review.avatar}
                                alt={review.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-[#0a0a0a] shadow-sm" />
                          </div>
                          <div>
                            <h4 className="text-white font-semibold text-sm mb-1">{review.name}</h4>
                            <p className="text-gray-400 text-xs">{review.location}</p>
                          </div>
                        </div>
                      </div>

                      {/* Shine effect */}
                      <div
                        className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                        style={{
                          background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%)'
                        }}
                      />

                      {/* Corner accent */}
                      <div className="absolute top-6 right-6 w-2 h-2 bg-[#404040] rounded-full group-hover:bg-[#6a6a6a] transition-colors duration-300 shadow-sm" />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Enhanced scroll indicators */}
            <div className="flex justify-center mt-1 gap-3">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollToTestimonial(index)}
                  className={`transition-all duration-300 rounded-full ${
                    index === testimonialScrollIndex
                      ? 'w-8 h-2 bg-[#f5f5f5] shadow-lg'
                      : 'w-2 h-2 bg-[#6a6a6a] hover:bg-[#9a9a9a]'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Desktop: Enhanced 3D Grid */}
        <div className="hidden md:grid md:grid-cols-3 gap-8">
          {testimonials.map((review, index) => (
            <div
              key={index}
              className="group transform-gpu transition-all duration-700 hover:scale-105 hover:-translate-y-2"
              style={{ perspective: '1000px' }}
            >
              <div
                className="relative bg-[#0a0a0a] backdrop-blur-xl p-8 rounded-3xl border border-[#2a2a2a] hover:border-[#404040] transition-all duration-500 h-full shadow-2xl"
                style={{
                  transform: 'rotateX(3deg) rotateY(-3deg)',
                  boxShadow: `
                    0 25px 50px -12px rgba(0, 0, 0, 0.8),
                    0 0 0 1px rgba(255, 255, 255, 0.05),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1)
                  `
                }}
              >
                {/* 3D depth layers */}
                <div
                  className="absolute inset-0 bg-gradient-to-br from-[#1a1a1a] to-transparent rounded-3xl"
                  style={{
                    transform: 'translateZ(-3px) translateX(-3px) translateY(-3px)'
                  }}
                />

                {/* Content */}
                <div className="relative z-10">
                  {/* Stars */}
                  <div className="flex items-center mb-6 gap-1">
                    {[...Array(review.rating)].map((_, i) => (
                      <Star
                        key={i}
                        size={20}
                        className="text-yellow-400 fill-yellow-400 drop-shadow-sm transform transition-transform duration-300 hover:scale-110"
                      />
                    ))}
                  </div>

                  {/* Quote */}
                  <div className="relative mb-8">
                    <div className="absolute -top-4 -left-3 text-7xl text-slate-600/20 font-serif leading-none">"</div>
                    <blockquote className="text-gray-200 text-lg leading-relaxed font-light relative z-10 pl-8 italic">
                      {review.text}
                    </blockquote>
                    <div className="absolute -bottom-6 -right-3 text-7xl text-slate-600/20 font-serif leading-none transform rotate-180">"</div>
                  </div>

                  {/* Profile */}
                  <div className="flex items-center gap-4">
                    <div
                      className="w-14 h-14 rounded-full overflow-hidden border-2 border-[#404040] shadow-lg transition-transform duration-300 hover:scale-110"
                      style={{
                        boxShadow: '0 8px 16px rgba(0, 0, 0, 0.4)'
                      }}
                    >
                      <img
                        src={review.avatar}
                        alt={review.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="text-white font-bold text-lg mb-1">{review.name}</div>
                      <div className="text-gray-400 text-sm">{review.location}</div>
                    </div>
                  </div>
                </div>

                {/* Shine effect */}
                <div
                  className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%)'
                  }}
                />

                {/* Corner accent */}
                <div className="absolute top-6 right-6 w-3 h-3 bg-[#404040] rounded-full group-hover:bg-[#6a6a6a] transition-colors duration-300 shadow-sm" />
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </motion.section>
  );
};

const SubscriptionSectionOptimized = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'}/subscriptions/simple`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          source: 'homepage'
        })
      });

      const data = await response.json();

      if (data.success) {
        setIsSubscribed(true);
        setEmail('');
        setTimeout(() => setIsSubscribed(false), 5000);
      } else {
        setError(data.message || 'Failed to subscribe. Please try again.');
      }
    } catch (err) {
      setError('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="py-20 bg-[#1a1a1a] relative overflow-hidden">
      <div
        className="absolute inset-0 opacity-30"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3Cpattern id='stars' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='10' cy='10' r='0.5' fill='rgba(255,255,255,0.1)'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23stars)'/%3E%3C/svg%3E")`
        }}
      />

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-5xl md:text-7xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-6"
            whileHover={{ scale: 1.02 }}
          >
            JOIN THE WOLFFOXX PACK
          </motion.h2>
          <p className="text-blue-200 text-xl mb-12 leading-relaxed">
            Subscribe for exclusive drops, early access to new collections, insider styling tips,
            <br className="hidden md:block" />
            and be the first to know about new arrivals and special events.
          </p>

          <AnimatePresence mode="wait">
            {!isSubscribed ? (
              <motion.form
                onSubmit={handleSubmit}
                className="flex flex-col md:flex-row gap-4 max-w-2xl mx-auto mb-8"
                initial={{ opacity: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex-1 relative">
                  <motion.input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className={`w-full px-6 py-4 rounded-xl border-2 ${
                      error ? 'border-red-500' : 'border-[#404040] focus:border-[#6a6a6a]'
                    } bg-[#0a0a0a] backdrop-blur-sm text-white placeholder-[#6a6a6a] focus:outline-none focus:ring-2 focus:ring-[#6a6a6a] transition-all`}
                    required
                    disabled={isLoading}
                    whileFocus={{ scale: 1.02 }}
                  />
                  {error && (
                    <p className="text-red-400 text-sm mt-2 absolute left-0 top-full">
                      {error}
                    </p>
                  )}
                </div>
                <motion.button
                  type="submit"
                  disabled={isLoading}
                  className={`px-8 py-4 ${
                    isLoading
                      ? 'bg-gray-600 cursor-not-allowed'
                      : 'bg-[#FF6B35] hover:bg-[#ff8c00]'
                  } text-white font-bold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl`}
                  whileHover={!isLoading ? { scale: 1.05 } : {}}
                  whileTap={!isLoading ? { scale: 0.95 } : {}}
                >
                  {isLoading ? 'SUBSCRIBING...' : 'SUBSCRIBE NOW'}
                </motion.button>
              </motion.form>
            ) : (
              <motion.div
                className="max-w-2xl mx-auto mb-8"
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-6 backdrop-blur-sm">
                  <motion.div
                    className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", delay: 0.2 }}
                  >
                    <motion.svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="white"
                      strokeWidth="3"
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                    >
                      <path d="M20 6L9 17l-5-5" />
                    </motion.svg>
                  </motion.div>
                  <h3 className="text-2xl font-bold text-white mb-2">Welcome to the Pack! 🐺</h3>
                  <p className="text-green-200">Check your email for your welcome message and exclusive updates!</p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <p className="text-blue-300 text-sm max-w-2xl mx-auto leading-relaxed">
            By subscribing, you agree to receive marketing emails from WOLFFOXX.
            You can unsubscribe at any time. We respect your privacy and will never share your information.
          </p>

          {/* Social Proof */}
          <motion.div
            className="flex items-center justify-center gap-3 sm:gap-6 md:gap-8 mt-12 text-blue-200 px-4"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.5 }}
          >
            <div className="flex items-center gap-1 sm:gap-2 flex-1 justify-center min-w-0">
              <span className="text-lg sm:text-xl md:text-2xl font-bold text-white">25K+</span>
              <span className="text-xs sm:text-sm whitespace-nowrap">Subscribers</span>
            </div>
            <div className="w-px h-6 sm:h-8 bg-[#404040]" />
            <div className="flex items-center gap-1 sm:gap-2 flex-1 justify-center min-w-0">
              <span className="text-lg sm:text-xl md:text-2xl font-bold text-white">4.9★</span>
              <span className="text-xs sm:text-sm whitespace-nowrap">Rating</span>
            </div>
            <div className="w-px h-6 sm:h-8 bg-[#404040]" />
            <div className="flex items-center gap-1 sm:gap-2 flex-1 justify-center min-w-0">
              <span className="text-lg sm:text-xl md:text-2xl font-bold text-white">95%</span>
              <span className="text-xs sm:text-sm whitespace-nowrap">Satisfaction</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

const InstagramFeedOptimized = () => {
  const instagramPosts = [
    'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_300,h_300,c_fill/v1752099103/ProductsImages/oversized5%281%29.png',
    'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_300,h_300,c_fill/v1752099099/ProductsImages/shirt1BL%281%29.png',
    'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_300,h_300,c_fill/v1752099694/ProductsImages/tshirt1%283%29.jpg',
    'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_300,h_300,c_fill/v1752099112/ProductsImages/shirt1W%281%29.png',
    'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_300,h_300,c_fill/v1752099107/ProductsImages/printedshirt1%281%29.png',
    'https://res.cloudinary.com/dsp0zmfcx/image/upload/f_auto,q_auto,w_300,h_300,c_fill/v1752099695/ProductsImages/tshirt2B%281%29.jpg'

  ];

  return (
    <section className="py-24 bg-black relative overflow-hidden">

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-5xl md:text-6xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-6"
            whileHover={{ scale: 1.02 }}
          >
            @WOLFFOXX
          </motion.h2>
          <p className="text-gray-400 text-lg max-w-3xl mx-auto mb-8">
            Follow us on Instagram for daily styling inspiration, behind-the-scenes content,
            and exclusive sneak peeks of upcoming drops.
          </p>

          <motion.a
            href="https://www.instagram.com/wolffoxx.x/"
            target="_blank"
            rel="noopener noreferrer"
className="inline-flex items-center gap-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white px-8 py-4 rounded-full font-bold transition-all duration-300 shadow-lg hover:shadow-xl"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
            Follow @WOLFFOXX
          </motion.a>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {instagramPosts.map((post, index) => (
            <motion.a
              key={index}
              href="https://www.instagram.com/wolffoxx.x/"
              target="_blank"
              rel="noopener noreferrer"
              className="group relative aspect-square overflow-hidden rounded-2xl cursor-pointer block"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ scale: 1.05, y: -5 }}
            >
              <img
                src={post}
                alt={`Instagram post ${index + 1}`}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                loading="lazy"
              />

              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-[#1a1a1a]/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300">
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex items-center justify-between text-white text-sm">
                    <div className="flex items-center gap-2">
                      <Heart size={16} className="fill-red-500 text-red-500" />
                      <span>{Math.floor(Math.random() * 500) + 100}</span>
                    </div>
                    <ArrowUpRight size={16} />
                  </div>
                </div>
              </div>

              {/* Instagram icon overlay */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-[#f5f5f5] backdrop-blur-sm rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg className="w-4 h-4 text-pink-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </div>
            </motion.a>
          ))}
        </div>

        {/* User Generated Content Section */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
        >
          <div className="bg-[#0a0a0a] backdrop-blur-sm rounded-2xl p-4 border border-[#2a2a2a]">
            <h3 className="text-2xl font-bold text-white mb-4">Show Off Your Style</h3>
            <p className="text-gray-400 mb-6">
              Tag us <span className="text-blue-400 font-medium">@wolffoxx</span> and
              use <span className="text-[#FF6B35] font-medium">#WolffoxxStyle</span> for a chance to be featured!
            </p>
            {/* <div className="flex flex-wrap justify-center gap-3">
              <span className="px-4 py-2 bg-[#2a2a2a] text-[#FF6B35] rounded-full text-sm font-medium">
                #WolffoxxStyle
              </span>
              <span className="px-4 py-2 bg-[#2a2a2a] text-[#FF6B35] rounded-full text-sm font-medium">
                #OversizedVibes
              </span>
              <span className="px-4 py-2 bg-[#2a2a2a] text-[#FF6B35] rounded-full text-sm font-medium">
                #StreetStyle
              </span>
            </div> */}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

// NEW FAQ Section Component
const FAQSection = () => {
  const [openFAQ, setOpenFAQ] = useState(null);

const faqs = [
  {
    question: "What makes WOLFFOXX tees different from regular oversized tees?",
    answer: "Our tees are crafted specifically for the Indian market with premium cotton, dropped shoulders, and an extended length for that perfect oversized look. We focus on comfort, durability, and unique graphics that don’t fade or shrink easily, even after multiple washes."
  },
  {
    question: "How do I choose the right size for an oversized fit?",
    answer: "We recommend picking 1-2 sizes larger than your regular size for the true oversized vibe. But don’t worry—we’ve added a detailed size chart on every product page to help you choose the perfect fit based on your chest and shoulder measurements."
  },
  {
    question: "What is your return and exchange policy?",
    answer: "We offer easy returns and exchanges within 7 days of delivery for unused products with original tags. Just drop us an <NAME_EMAIL> or WhatsApp us for a pickup. Refunds are processed as store credit or original payment method depending on the case."
  },
  {
    question: "How long does shipping take?",
    answer: "All orders are dispatched within 24 hours from our warehouse. Once shipped, delivery usually takes 2-5 business days depending on your location. You’ll receive tracking details on SMS and email."
  },
  {
    question: "Is shipping free?",
    answer: "Yes! We offer free shipping on all orders across India, no minimum order value required."
  },
  {
    question: "Do you restock sold-out items?",
    answer: "We try to restock popular pieces within 3-4 weeks. Some limited edition drops may not be restocked. You can sign up for back-in-stock alerts on our website or follow us on Instagram for updates."
  },
  {
    question: "Do you offer bulk or wholesale orders?",
    answer: "Yes, we handle bulk orders for teams, colleges, or corporate events. We also offer wholesale pricing to selected retailers. For more info, email <NAME_EMAIL>."
  }
];


  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <section className="pt-6 pb-12 bg-black relative overflow-hidden">

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-5xl md:text-7xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-6"
            whileHover={{ scale: 1.02 }}
          >
            FREQUENTLY ASKED QUESTIONS
          </motion.h2>
          <p className="text-gray-400 text-lg max-w-3xl mx-auto leading-relaxed">
            Got questions? We've got answers. Find everything you need to know about
            WOLFFOXX products, shipping, sizing, and more.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                className="bg-[#0a0a0a] backdrop-blur-sm rounded-2xl border border-[#2a2a2a] overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <motion.button
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-[#1a1a1a] transition-colors duration-300"
                  onClick={() => toggleFAQ(index)}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  <h3 className="text-white font-bold text-lg pr-4 leading-relaxed">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openFAQ === index ? 45 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex-shrink-0"
                  >
                    <Plus
                      size={24}
                      className={`transition-colors duration-300 ${
                        openFAQ === index ? 'text-[#FF6B35]' : 'text-gray-400'
                      }`}
                    />
                  </motion.div>
                </motion.button>

                <AnimatePresence>
                  {openFAQ === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6">
                        <motion.div
                          initial={{ y: -10, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          transition={{ duration: 0.3, delay: 0.1 }}
                          className="border-t border-[#2a2a2a] pt-4"
                        >
                          <p className="text-gray-300 leading-relaxed text-base">
                            {faq.answer}
                          </p>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>

          {/* Contact Support Section */}
          <motion.div
            className="mt-8 text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.5 }}
          >
            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl p-6 border border-[#2a2a2a]">
              <h3 className="text-xl font-bold text-[#f5f5f5] mb-3">Still have questions?</h3>
              <p className="text-[#9a9a9a] mb-4 max-w-2xl mx-auto">
                Our customer support team is here to help! Reach out via email, live chat,
                or social media for personalized assistance.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <motion.a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center gap-2 bg-[#333333] hover:bg-[#404040] text-[#FFFFFF] px-6 py-3 rounded-lg font-medium transition-colors border border-[#555555]"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Email Support
                  <ArrowUpRight size={16} />
                </motion.a>
                <motion.button
                  className="inline-flex items-center gap-2 bg-[#FF6B35] hover:bg-[#e55a2b] text-[#FFFFFF] px-6 py-3 rounded-lg font-medium transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.open('https://wa.me/919253605603', '_blank')}
                >
                  Live Chat
                  <ArrowUpRight size={16} />
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

// BestSellerCard Component - Styled like FeaturedCollection
function BestSellerCard({ product, index, isVisible }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);

  // Get current images based on selected color
  const getCurrentImages = () => {
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  const currentImages = getCurrentImages();

  const nextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
  };

  const prevImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev - 1 + currentImages.length) % currentImages.length);
  };

  const goToImage = (idx, e) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex(idx);
  };

  const handleColorSelect = (idx) => {
    setSelectedColorIndex(idx);
    // Reset to first image when color changes
    setCurrentImageIndex(0);
  };



  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="bg-[#0a0a0a] rounded-xl overflow-hidden group border border-[#2a2a2a]"
    >
      <Link to={`/product/${product.id}`} className="block relative">
        {/* Image Carousel Container */}
        <div className="h-72 md:h-80 overflow-hidden relative">
          <div className="relative w-full h-full">
            {currentImages.map((image, idx) => (
              <img
                key={idx}
                src={image && image.includes('cloudinary.com')
                  ? image.replace('/upload/', '/upload/w_400,h_400,c_fill,f_auto,q_auto/')
                  : image}
                alt={`${product.name} - ${product.colors[selectedColorIndex]?.name || 'Default'} - Image ${idx + 1}`}
                loading="lazy"
                className={`absolute inset-0 w-full h-full object-cover transform group-hover:scale-105 transition-all duration-700 ease-in-out ${
                  idx === currentImageIndex
                    ? 'opacity-100 z-10'
                    : 'opacity-0 z-0'
                }`}
                onError={(e) => {
                  e.target.src = '/api/placeholder/400/400';
                }}
              />
            ))}
          </div>

          {/* Navigation Arrows - Always visible */}
          {currentImages.length > 1 && (
            <>
              <button
                onClick={prevImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronLeft size={16} />
              </button>
              <button
                onClick={nextImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronRight size={16} />
              </button>
            </>
          )}



          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
          {product.isNew && (
            <div className="bg-[#6a6a6a] text-[#f5f5f5] text-xs font-medium px-2 py-1 rounded-md shadow-lg">
              NEW
            </div>
          )}
          {product.salePrice && (
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-medium px-2 py-1 rounded-md shadow-lg">
              SALE
            </div>
          )}
        </div>

        {/* Image Counter */}
        {currentImages.length > 1 && (
          <div className="absolute top-3 right-12 bg-black/50 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm">
            {currentImageIndex + 1}/{currentImages.length}
          </div>
        )}

        {/* Wishlist Button */}
        <div className="absolute top-3 right-3 z-10">
          <WishlistButton
            productId={product.id}
            productName={product.name}
            productPrice={product.salePrice || product.price}
            productImage={currentImages[0] || product.images?.[0]}
            className="bg-[#2a2a2a] hover:bg-[#404040] text-[#d4d4d4] w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200"
          />
        </div>

        {/* Quick Actions Overlay */}
        {/* <BestSellerAnimatedInfo product={product} isVisible={isHovered} /> */}
      </Link>

      {/* Product Info */}
      <div className="p-4 md:p-5">
        <div className="mb-3">
          <span className="text-[#6a6a6a] text-xs font-normal uppercase tracking-wider opacity-80">
            {product.category}
          </span>
          <h3 className="text-[#d4d4d4] font-semibold text-lg hover:text-[#9a9a9a] transition-colors mt-1 line-clamp-1 truncate">
            <Link to={`/product/${product.id}`} className="block truncate" title={product.name}>{product.name}</Link>
          </h3>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {product.salePrice ? (
              <>
                <span className="text-[#f5f5f5] font-bold text-lg">${product.salePrice}</span>
                <span className="text-[#6a6a6a] line-through text-sm">${product.price}</span>
                <span className="text-xs bg-[#404040] text-[#d4d4d4] px-2 py-1 rounded-full font-medium">
                  {Math.round(((product.price - product.salePrice) / product.price) * 100)}% OFF
                </span>
              </>
            ) : (
              <span className="text-[#f5f5f5] font-bold text-lg">${product.price}</span>
            )}
          </div>
        </div>

        {/* Color Selection */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex gap-1.5">
              {product.colors.slice(0, 4).map((color, idx) => (
                <button
                  key={idx}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleColorSelect(idx);
                  }}
                  className={`w-5 h-5 rounded-full border-2 transition-all duration-200 ${
                    selectedColorIndex === idx
                      ? 'border-[#f5f5f5] scale-110 shadow-lg shadow-white/25'
                      : 'border-[#404040] hover:border-[#6a6a6a]'
                  }`}
                  style={{ backgroundColor: color.value }}
                  title={color.name}
                />
              ))}
              {product.colors.length > 4 && (
                <div className="w-5 h-5 rounded-full bg-[#2a2a2a] flex items-center justify-center text-xs text-[#9a9a9a] font-medium">
                  +{product.colors.length - 4}
                </div>
              )}
            </div>
          </div>

          {/* Stock Status */}
          <div className="text-xs text-[#6a6a6a]">
            {product.stock > 0 ? (
              <span className="text-[#9a9a9a]">● In Stock</span>
            ) : (
              <span className="text-[#9a9a9a]">● In Stock</span>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}

// function BestSellerAnimatedInfo({ product, isVisible }) {
//   return (
//     <motion.div
//       initial={false}
//       animate={{
//         opacity: isVisible ? 1 : 0,
//         y: isVisible ? 0 : 20,
//       }}
//       transition={{ duration: 0.2 }}
//       className="absolute inset-x-0 bottom-0 p-4 md:p-5 bg-gradient-to-t from-gray-900/95 via-gray-900/80 to-transparent backdrop-blur-sm"
//     >
//       <div className="flex items-center justify-between gap-3">
//         <div className="flex-1">
//           <div className="text-white font-medium text-sm">{product.name}</div>
//           <div className="text-gray-300 text-xs">{product.category}</div>
//         </div>

//         <div className="flex gap-2">
//           <Link
//             to={`/product/${product.id}`}
//             className="bg-white/10 hover:bg-white/20 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-all duration-200 backdrop-blur-sm border border-white/20"
//           >
//             <Eye size={12} />
//             Quick View
//           </Link>
//           <button className="bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-colors">
//             <ShoppingBag size={12} />
//             Add to Cart
//           </button>
//         </div>
//       </div>
//     </motion.div>
//   );
// }