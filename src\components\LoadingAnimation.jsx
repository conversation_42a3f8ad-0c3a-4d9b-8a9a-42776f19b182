import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import logo45 from '../assets/logo30.svg';

const LoadingAnimation = ({ onComplete }) => {
  const [isComplete, setIsComplete] = useState(false);
  const [startExit, setStartExit] = useState(false);

  useEffect(() => {
    // Prevent scrolling during animation
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // Start exit animation after 1.8 seconds
    const exitTimer = setTimeout(() => setStartExit(true), 1800);

    // Complete animation after 2.8 seconds
    const completeTimer = setTimeout(() => {
      setIsComplete(true);
      // Restore scrolling
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      onComplete?.();
    }, 2800);

    return () => {
      clearTimeout(exitTimer);
      clearTimeout(completeTimer);
      // Ensure scrolling is restored
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
    };
  }, [onComplete]);

  // Custom easing curve
  const customEasing = [0.16, 1, 0.3, 1];

  return (
    <AnimatePresence mode="wait">
      {!isComplete && (
        <motion.div
          initial={{ y: 0 }}
          animate={{
            y: startExit ? '-100vh' : 0,
          }}
          exit={{ y: '-100vh' }}
          transition={{
            duration: 1.2,
            ease: customEasing,
            type: "tween"
          }}
          className="loading-animation"
        >
          {/* Logo */}
          <div className="loading-logo">
            <img
              src={logo45}
              alt="Wolffoxx Logo"
            />
          </div>

          {/* Brand Text */}
          <div className="loading-text">
            WOLFFOXX
          </div>

          {/* Subtitle */}
          <div className="loading-subtitle">
            {/* Add your tagline here if needed */}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoadingAnimation;
