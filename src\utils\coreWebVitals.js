/**
 * Core Web Vitals Monitoring
 * Tracks TTFB, FCP, LCP, CLS, FID for performance optimization
 */

class CoreWebVitalsMonitor {
  constructor() {
    this.metrics = {};
    this.observers = [];
    this.init();
  }

  init() {
    // Track TTFB (Time to First Byte)
    this.trackTTFB();
    
    // Track FCP (First Contentful Paint)
    this.trackFCP();
    
    // Track LCP (Largest Contentful Paint)
    this.trackLCP();
    
    // Track CLS (Cumulative Layout Shift)
    this.trackCLS();
    
    // Track FID (First Input Delay)
    this.trackFID();
    
    // Track custom metrics
    this.trackCustomMetrics();
  }

  trackTTFB() {
    try {
      const navigationEntry = performance.getEntriesByType('navigation')[0];
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        this.metrics.ttfb = Math.round(ttfb);
        this.logMetric('TTFB', ttfb, ttfb < 600 ? 'good' : ttfb < 1000 ? 'needs-improvement' : 'poor');
      }
    } catch (error) {
      console.warn('TTFB tracking failed:', error);
    }
  }

  trackFCP() {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            const fcp = entry.startTime;
            this.metrics.fcp = Math.round(fcp);
            this.logMetric('FCP', fcp, fcp < 1800 ? 'good' : fcp < 3000 ? 'needs-improvement' : 'poor');
            observer.disconnect();
          }
        }
      });
      observer.observe({ entryTypes: ['paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('FCP tracking failed:', error);
    }
  }

  trackLCP() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        const lcp = lastEntry.startTime;
        this.metrics.lcp = Math.round(lcp);
        this.logMetric('LCP', lcp, lcp < 2500 ? 'good' : lcp < 4000 ? 'needs-improvement' : 'poor');
      });
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('LCP tracking failed:', error);
    }
  }

  trackCLS() {
    try {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        this.metrics.cls = Math.round(clsValue * 1000) / 1000;
        this.logMetric('CLS', clsValue, clsValue < 0.1 ? 'good' : clsValue < 0.25 ? 'needs-improvement' : 'poor');
      });
      observer.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('CLS tracking failed:', error);
    }
  }

  trackFID() {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fid = entry.processingStart - entry.startTime;
          this.metrics.fid = Math.round(fid);
          this.logMetric('FID', fid, fid < 100 ? 'good' : fid < 300 ? 'needs-improvement' : 'poor');
          observer.disconnect();
        }
      });
      observer.observe({ entryTypes: ['first-input'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('FID tracking failed:', error);
    }
  }

  trackCustomMetrics() {
    // Track page load time
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      this.metrics.pageLoadTime = Math.round(loadTime);
      this.logMetric('Page Load Time', loadTime, loadTime < 3000 ? 'good' : loadTime < 5000 ? 'needs-improvement' : 'poor');
    });

    // Track DOM content loaded
    document.addEventListener('DOMContentLoaded', () => {
      const domLoadTime = performance.now();
      this.metrics.domLoadTime = Math.round(domLoadTime);
      this.logMetric('DOM Load Time', domLoadTime, domLoadTime < 1500 ? 'good' : domLoadTime < 2500 ? 'needs-improvement' : 'poor');
    });
  }

  logMetric(name, value, rating) {
    const color = rating === 'good' ? '#0CCE6B' : rating === 'needs-improvement' ? '#FFA400' : '#FF4E42';
    console.log(
      `%c📊 ${name}: ${Math.round(value)}ms (${rating})`,
      `color: ${color}; font-weight: bold;`
    );
  }

  getMetrics() {
    return { ...this.metrics };
  }

  getReport() {
    return {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      metrics: this.getMetrics(),
      recommendations: this.getRecommendations()
    };
  }

  getRecommendations() {
    const recommendations = [];
    const { ttfb, fcp, lcp, cls, fid } = this.metrics;

    if (ttfb > 600) {
      recommendations.push('Optimize server response time (TTFB > 600ms)');
    }
    if (fcp > 1800) {
      recommendations.push('Optimize First Contentful Paint (FCP > 1.8s)');
    }
    if (lcp > 2500) {
      recommendations.push('Optimize Largest Contentful Paint (LCP > 2.5s)');
    }
    if (cls > 0.1) {
      recommendations.push('Reduce Cumulative Layout Shift (CLS > 0.1)');
    }
    if (fid > 100) {
      recommendations.push('Optimize First Input Delay (FID > 100ms)');
    }

    return recommendations;
  }

  // Send metrics to analytics (implement as needed)
  sendToAnalytics() {
    const report = this.getReport();
    console.log('📈 Performance Report:', report);
    
    // TODO: Send to your analytics service
    // Example: analytics.track('core_web_vitals', report);
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Create global instance
const coreWebVitalsMonitor = new CoreWebVitalsMonitor();

// Auto-send report after page load
window.addEventListener('load', () => {
  setTimeout(() => {
    coreWebVitalsMonitor.sendToAnalytics();
  }, 5000); // Wait 5 seconds for all metrics to be collected
});

export default coreWebVitalsMonitor;
